package services

import (
	"auth-service/config"
	reqDTO "auth-service/dtos/Request"
	resDTO "auth-service/dtos/Response"
	"auth-service/models"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
)

func AdminLogin(c echo.Context) error {
	var req reqDTO.AdminLoginRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			Message: "Invalid request",
		})
	}
	// pass, _ := HashPassword(req.Password)
	// print(pass)
	// Find the admin by email
	var admin models.Admin
	if err := config.DB.Where("email = ?", req.Email).First(&admin).Error; err != nil {
		return c.JSON(http.StatusUnauthorized, resDTO.APIResponse{
			Message: "Invalid credentials",
		})
	}

	// Verify password
	if !CheckPasswordHash(req.Password, admin.Password) {
		return c.JSON(http.StatusUnauthorized, resDTO.APIResponse{
			Message: "Invalid credentials",
		})
	}

	// Generate JWT token with admin role
	token, err := GenerateAdminJWT(admin.Email, admin.ID, admin.Role)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Error generating token",
		})
	}

	// Parse permissions
	var permissions []string
	if err := json.Unmarshal([]byte(admin.Permissions), &permissions); err != nil {
		permissions = []string{} // Default to empty if parsing fails
	}

	// Update last login
	config.DB.Model(&admin).Update("last_login", time.Now())
	err1 := SendOTPEmail(admin.Email, "Sign in", "You've signed in successfully")
	if err1 != nil {

		fmt.Println("Error sending email:", err)
	}
	return c.JSON(http.StatusOK, resDTO.AdminLoginResponse{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "Login successful",
		},
		Token:       token,
		Role:        admin.Role,
		Permissions: permissions,
		User: resDTO.AdminDTO{
			ID:        admin.ID,
			Email:     admin.Email,
			FirstName: admin.FirstName,
			LastName:  admin.LastName,
			Role:      admin.Role,
			LastLogin: admin.LastLogin,
		},
	})
}

func CreateAdmin(c echo.Context) error {
	var req reqDTO.AdminCreateRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			Message: "Invalid request",
		})
	}

	// Check if admin already exists
	var existingAdmin models.Admin
	if err := config.DB.Where("email = ?", req.Email).First(&existingAdmin).Error; err == nil {
		return c.JSON(http.StatusConflict, resDTO.APIResponse{
			Message: "Admin with this email already exists",
		})
	}

	// Hash password
	hashedPassword, err := HashPassword(req.Password)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Error hashing password",
		})
	}

	// Convert permissions to JSON
	permissionsJSON, err := json.Marshal(req.Permissions)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Error processing permissions",
		})
	}

	// Create admin
	admin := models.Admin{
		Email:       req.Email,
		Password:    hashedPassword,
		FirstName:   req.FirstName,
		LastName:    req.LastName,
		Role:        req.Role,
		Permissions: string(permissionsJSON),
	}

	if err := config.DB.Create(&admin).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Error creating admin",
		})
	}

	return c.JSON(http.StatusCreated, resDTO.APIResponse{
		IsSuccess: true,
		Message:   "Admin created successfully",
	})
}

// Add this to your existing admin_service.go file

func GetPendingKYCVerifications(c echo.Context) error {
	var verifications []resDTO.KYCVerificationDTO = make([]resDTO.KYCVerificationDTO, 0)

	// Execute the query
	err := config.DB.Raw(`
        SELECT 
            u.id as user_id,
            kv.id as verification_id,
            CONCAT(u.first_name, " ", u.last_name) as full_name,
            kv.created_at as request_date,
            u.ws_id as wabisabi_id,
            "Identity Verification" as verification_type
        FROM kyc_verifications kv
        INNER JOIN user_details ud ON ud.user_id = kv.user_id
        INNER JOIN users u ON u.id = kv.user_id
        WHERE kv.flagged_for_manual_review = true OR kv.status = "pending"
    `).Scan(&verifications).Error

	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Error fetching KYC verifications",
		})
	}

	return c.JSON(http.StatusOK, resDTO.KYCVerificationListResponse{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "KYC verifications fetched successfully",
		},
		Verifications: verifications,
	})
}

// Add this to your existing admin_service.go file

func GetKYCVerificationDetails(c echo.Context) error {
	// Get user ID from URL parameter
	adminID, err := InterfaceToUint(c.Get("admin_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error converting user id"})
	}
	print(adminID)
	userID := c.QueryParam("userId")
	userIDUint64, err := strconv.ParseUint(userID, 10, 32)
	if err != nil {
		return c.JSON(http.StatusBadRequest, map[string]string{"error": "invalid userId"})
	}

	var details resDTO.KYCVerificationDetailsDTO

	// Execute the query
	err = config.DB.Raw(`
        SELECT
		u.email,
            u.phone_number,
            ud.nic_url_front,
            ud.nic_url_back,
            ud.user_photo_url,
            u.ws_id as wabisabi_id,kv.id as verification_id,
            CONCAT(u.first_name, " ", u.last_name) as full_name,
            kv.created_at as request_date,
            "Identity Verification" as verification_type
        FROM kyc_verifications kv
        INNER JOIN user_details ud ON ud.user_id = kv.user_id
        INNER JOIN users u ON u.id = kv.user_id
        WHERE u.id = ?
    `, userIDUint64).Scan(&details).Error

	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Error fetching KYC verification details",
		})
	}

	// Check if any data was found
	if details.PhoneNumber == "" {
		return c.JSON(http.StatusNotFound, resDTO.APIResponse{
			Message: "KYC verification details not found",
		})
	}

	return c.JSON(http.StatusOK, resDTO.KYCVerificationDetailsResponse{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "KYC verification details fetched successfully",
		},
		Details: details,
	})
}

// Add this to your existing admin_service.go file

func UpdateAdminDetails(c echo.Context) error {
	// Get admin ID from the JWT token
	adminID, err := InterfaceToUint(c.Get("admin_id"))
	if err != nil {
		return c.JSON(http.StatusUnauthorized, resDTO.APIResponse{
			Message: "Invalid admin session",
		})
	}

	var req reqDTO.AdminUpdateRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			Message: "Invalid request",
		})
	}

	// Start a transaction
	tx := config.DB.Begin()

	// Get current admin details
	var admin models.Admin
	if err := tx.First(&admin, adminID).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusNotFound, resDTO.APIResponse{
			Message: "Admin not found",
		})
	}

	// Update fields if provided
	updates := make(map[string]interface{})

	if req.FirstName != "" {
		updates["first_name"] = req.FirstName
	}
	if req.LastName != "" {
		updates["last_name"] = req.LastName
	}

	// If no updates provided
	if len(updates) == 0 {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			Message: "No updates provided",
		})
	}

	// Update the admin
	if err := tx.Model(&admin).Updates(updates).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Error updating admin details",
		})
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Error committing changes",
		})
	}

	return c.JSON(http.StatusOK, resDTO.APIResponse{
		IsSuccess: true,
		Message:   "Admin details updated successfully",
	})
}

func HandleKYCVerificationAction(c echo.Context) error {
	// // Get verification ID from URL parameter
	// verificationID, err := InterfaceToUint(c.Get("verification_id"))
	// if err != nil {
	// 	return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
	// 		Message: "Invalid verification ID",
	// 	})
	// }
	verificationID := c.QueryParam("verificationId")
	if verificationID == "" {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			Message: "verification ID not found"})
	}
	// Parse request body
	var req reqDTO.KYCVerificationActionDTO
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			Message: "Invalid request format",
		})
	}

	// Validate action
	if req.Action != "verified" && req.Action != "rejected" {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			Message: "Invalid action. Must be 'approve' or 'reject'",
		})
	}

	// Start a transaction
	tx := config.DB.Begin()
	if tx.Error != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Failed to start transaction",
		})
	}

	// Get the KYC verification
	var kycVerification models.KYCVerification
	if err := tx.Where("id = ?", verificationID).First(&kycVerification).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusNotFound, resDTO.APIResponse{
			Message: "KYC verification not found",
		})
	}

	// Update KYC verification status
	updates := map[string]interface{}{
		"flagged_for_manual_review": false,
	}

	// Set status based on action
	if req.Action == "approve" {
		updates["status"] = "verified"
	} else {
		updates["status"] = "rejected"
		if req.Reason != "" {
			updates["last_error_code"] = req.Reason
		}
	}

	if err := tx.Model(&kycVerification).Updates(updates).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Failed to update KYC verification status",
		})
	}

	// If approved, update user details
	// if req.Action == "approve" {
	// 	if err := tx.Model(&models.UserDetails{}).
	// 		Where("user_id = ?", kycVerification.UserID).
	// 		Update("is_verify", true).Error; err != nil {
	// 		tx.Rollback()
	// 		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
	// 			Message: "Failed to update user verification status",
	// 		})
	// 	}
	// }

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Failed to commit transaction",
		})
	}

	return c.JSON(http.StatusOK, resDTO.APIResponse{
		IsSuccess: true,
		Message:   "KYC verification " + req.Action + "d successfully",
	})
}
