package services

import (
	"auth-service/config"
	resDTO "auth-service/dtos/Response"
	"encoding/base64"
	"encoding/json"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/skip2/go-qrcode"
)

func GenerateQRCode(c echo.Context) error {
	// either we get ws_id and other stuff from mobile client
	// or we do it on the server using userID
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{IsSuccess: false, Message: "Error converting user ID"})
	}

	// db function to get ws id and other stuff associated with the userID
	qrRequestDTO, err := getUserByID(config.DB, userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{IsSuccess: false, Message: "Error fetching user data"})
	}

	// create a json string of the data to be embedded so that client can parse with ease
	jsonData, err := json.Marshal(qrRequestDTO)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{IsSuccess: false, Message: "Error encoding JSON"})
	}

	// generate a QR code here embedding ws_id and other information and send it to the client as Base 64
	qrImage, err := qrcode.Encode(string(jsonData), qrcode.Highest, 256)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{IsSuccess: false, Message: "Error generating QR code"})
	}

	qrCodeBase64 := base64.StdEncoding.EncodeToString(qrImage)

	response := resDTO.QRResponseDTO{APIResponse: resDTO.APIResponse{IsSuccess: true, Message: "QR generated successfully"}, ImageBase64: qrCodeBase64}

	return c.JSON(http.StatusOK, response)

}
