package services

import (
	resDTO "auth-service/dtos/Response"
	"auth-service/models"
	"encoding/json"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"regexp"
	"strings"

	"github.com/labstack/echo/v4"
)

type IBANResponse struct {
	IBAN     string `json:"iban"`
	BankData struct {
		Name string `json:"name"`
		BIC  string `json:"bic"`
	} `json:"bankData"`
}

func ValidateIban(c echo.Context) error {
	c.Set("serviceName", "account_service")
	c.Set("moduleName", "services")
	var req models.BankDetails
	var response resDTO.APIResponse
	if err := c.Bind(&req); err != nil {
		response = resDTO.APIResponse{Message: "Invalid request payload"}
		return c.JSON(http.StatusBadRequest, response)
	}
	iban := req.IBAN
	if iban == "" {
		response := resDTO.APIResponse{Message: "IBAN is required"}

		return c.JSON(http.StatusBadRequest, response)
	}
	if !ValidateIBAN(iban) {
		response = resDTO.APIResponse{Message: "Invalid IBAN"}
		return c.JSON(http.StatusBadRequest, response)
	}
	// response = res.APIResponse{IsSuccess: true, Message: "Valid IBAN"}
	bankName, err := GetBankNameFromIBAN(iban)
	if err != nil {
		response = resDTO.APIResponse{Message: "Error retrieving bank name"}
		return c.JSON(http.StatusInternalServerError, response)
	}
	response = resDTO.APIResponse{IsSuccess: true, Message: bankName}

	return c.JSON(http.StatusOK, response)
}

func ValidateIBAN(iban string) bool {
	// Remove spaces and convert to uppercase
	iban = strings.ToUpper(strings.ReplaceAll(iban, " ", ""))

	// Check if IBAN matches basic pattern
	matched, _ := regexp.MatchString(`^[A-Z]{2}[0-9]{2}[A-Z0-9]{1,30}$`, iban)
	if !matched {
		return false
	}

	// Move first 4 characters to the end
	iban = iban[4:] + iban[:4]

	// Convert letters to numbers (A=10, B=11, ..., Z=35)
	ibanNumeric := ""
	for _, char := range iban {
		if char >= 'A' && char <= 'Z' {
			ibanNumeric += fmt.Sprintf("%d", char-'A'+10)
		} else {
			ibanNumeric += string(char)
		}
	}

	// Convert to big integer and check MOD 97 == 1
	bigIntIban, _ := new(big.Int).SetString(ibanNumeric, 10)
	return new(big.Int).Mod(bigIntIban, big.NewInt(97)).Int64() == 1
}

func GetBankNameFromIBAN(iban string) (string, error) {
	url := fmt.Sprintf("https://openiban.com/validate/%s?getBIC=true&validateBankCode=true", iban)
	resp, err := http.Get(url)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	var ibanResponse IBANResponse
	if err := json.Unmarshal(body, &ibanResponse); err != nil {
		return "", err
	}

	if ibanResponse.BankData.Name == "" {
		return "", fmt.Errorf("bank name not found for IBAN: %s", iban)
	}

	return ibanResponse.BankData.Name, nil

}
