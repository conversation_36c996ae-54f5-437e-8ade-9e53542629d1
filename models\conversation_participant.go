package models

import (
	"time"

	"gorm.io/gorm"
)

type ConversationParticipant struct {
	gorm.Model

	UserID         uint      `gorm:"not null" json:"user_id"`
	ConversationID uint      `gorm:"not null" json:"conversation_id"`
	JoinedAt       time.Time `gorm:"type:datetime(3);default:CURRENT_TIMESTAMP(3)" json:"joined_at"`

	Conversation Conversation `gorm:"foreignKey:ConversationID;references:ID;constraint:OnDelete:CASCADE" json:"conversation"`
	User         User         `gorm:"foreignKey:UserID;references:Id;constraint:OnDelete:CASCADE" json:"user"`
}
