package services

import (
	resDTO "auth-service/dtos/Response"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
)

func GetGraphKeys(c echo.Context) error {
	_, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error converting user ID"})
	}

	result, err := getGraphKeysDB()
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Couldn't fetch keys from DB"})
	}

	return c.JSON(http.StatusOK, resDTO.GraphKeyResponseDTO{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message: "successfully fetched graph keys",
		},
		GraphKeys: result,
	})
}

// dont refactor function calls out of switch-case for the moment since the exact requirements
// of graph data are not clear -- whether they want aggregated graph_data or single data points
func GetGraphValues(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error converting user ID"})
	}

	graphKeyId := c.QueryParam("id")
	graphKeyValue, err := getValueofGraphKeyId(graphKeyId)
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			IsSuccess: false,
			Message:   "invalid graph key value",
		})
	}

	pktLoc, err := time.LoadLocation("Asia/Karachi")
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to load PKT timezone"})
	}
	
	var graphData []resDTO.AggregatedGraphDataUnix
	var allTransactions []resDTO.GraphTransactionUnix
	var msg string

	switch graphKeyValue {	
	case "today":
		today := time.Now().In(pktLoc).Format("2006-01-02")

		graphData, allTransactions, err = TransactionHistoryDetailsGraphForSingleDayUnix(userID, today, today, pktLoc)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to fetch today's transactions"})
		}
		msg = "Successfully fetched today's transaction history"

	case "yesterday":
		yesterday := time.Now().In(pktLoc).AddDate(0, 0, -1).Format("2006-01-02")

		graphData, allTransactions, err = TransactionHistoryDetailsGraphForSingleDayUnix(userID, yesterday, yesterday, pktLoc)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to fetch yesterday's transactions"})
		}
		msg = "Successfully fetched yesterday's transaction history"

	case "seven days":
		last7Start := time.Now().In(pktLoc).AddDate(0, 0, -7).Format("2006-01-02")
		last7End := time.Now().In(pktLoc).AddDate(0, 0, -1).Format("2006-01-02")

		graphData, allTransactions, err = TransactionHistoryDetailsGraphForSingleDayUnix(userID, last7Start, last7End, pktLoc)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to fetch 7-day transaction history"})
		}
		msg = "Successfully fetched last 7 day's transaction history"

	case "one month":
		lastMonthStart := time.Now().In(pktLoc).AddDate(0, 0, -30).Format("2006-01-02")
		lastMonthEnd := time.Now().In(pktLoc).AddDate(0, 0, -1).Format("2006-01-02")

		graphData, allTransactions, err = TransactionHistoryDetailsGraphForSingleDayUnix(userID, lastMonthStart, lastMonthEnd, pktLoc)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to fetch last month's transaction history"})
		}
		msg = "Successfully fetched last month's transaction history"
	
	case "three months":
		lastMonthStart := time.Now().In(pktLoc).AddDate(0, 0, -30 * 3).Format("2006-01-02")
		lastMonthEnd := time.Now().In(pktLoc).AddDate(0, 0, -1).Format("2006-01-02")

		graphData, allTransactions, err = TransactionHistoryDetailsGraphForSingleDayUnix(userID, lastMonthStart, lastMonthEnd, pktLoc)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to fetch last month's transaction history"})
		}
		msg = "Successfully fetched 3 month's transaction history"
		
	case "six months":
		lastMonthStart := time.Now().In(pktLoc).AddDate(0, 0, -30 * 6).Format("2006-01-02")
		lastMonthEnd := time.Now().In(pktLoc).AddDate(0, 0, -1).Format("2006-01-02")

		graphData, allTransactions, err = TransactionHistoryDetailsGraphForSingleDayUnix(userID, lastMonthStart, lastMonthEnd, pktLoc)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to fetch last month's transaction history"})
		}
		msg = "Successfully fetched last 6 month's transaction history"

	case "one year":
		lastMonthStart := time.Now().In(pktLoc).AddDate(0, 0, -30 * 12).Format("2006-01-02")
		lastMonthEnd := time.Now().In(pktLoc).AddDate(0, 0, -1).Format("2006-01-02")

		graphData, allTransactions, err = TransactionHistoryDetailsGraphForSingleDayUnix(userID, lastMonthStart, lastMonthEnd, pktLoc)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to fetch last month's transaction history"})
		}
		msg = "Successfully fetched year's transaction history"
	}

	return c.JSON(http.StatusOK, resDTO.GraphTransaction7DaysUnixResponseDTO{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   msg,
		},
		GraphData: graphData,
		TransactionHistory: allTransactions,
		})
}