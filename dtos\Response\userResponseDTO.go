package ResponseDTO

import "time"

type UserResponseDTO struct {
	Id          uint      `json:"id"`
	Email       string    `json:"email"`
	FullName    string    `json:"full_name"`
	FirstName   string    `json:"first_name"`
	LastName    string    `json:"last_name"`
	PhoneNumber string    `json:"phone_number,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	Token       string    `json:"token"`
}
