package services

import (
	"fmt"
	"strings"

	"github.com/nyaruka/phonenumbers"
)

// PhoneValidationResult contains the result of phone number validation
type PhoneValidationResult struct {
	IsValid         bool   `json:"is_valid"`
	FormattedNumber string `json:"formatted_number"`
	CountryCode     string `json:"country_code"`
	NationalNumber  string `json:"national_number"`
	ErrorMessage    string `json:"error_message,omitempty"`
}

// ValidatePhoneNumberFormat validates and formats a phone number
func ValidatePhoneNumberFormat(phoneNumber string, defaultRegion string) *PhoneValidationResult {
	result := &PhoneValidationResult{}
	
	// Clean the input
	phoneNumber = strings.TrimSpace(phoneNumber)
	if phoneNumber == "" {
		result.ErrorMessage = "Phone number cannot be empty"
		return result
	}

	// Try parsing with default region first
	if defaultRegion == "" {
		defaultRegion = "PK" // Default to Pakistan as seen in contact_service.go
	}
	
	num, err := phonenumbers.Parse(phoneNumber, defaultRegion)
	if err != nil {
		// Try parsing as international number
		num, err = phonenumbers.Parse(phoneNumber, "")
		if err != nil {
			result.ErrorMessage = fmt.Sprintf("Invalid phone number format: %v", err)
			return result
		}
	}

	// Validate the number
	if !phonenumbers.IsValidNumber(num) {
		result.ErrorMessage = "Phone number is not valid"
		return result
	}

	// Extract information
	result.IsValid = true
	result.CountryCode = fmt.Sprintf("+%d", num.GetCountryCode())
	result.NationalNumber = fmt.Sprintf("%d", num.GetNationalNumber())
	result.FormattedNumber = phonenumbers.Format(num, phonenumbers.E164)

	return result
}

// ValidatePhoneNumberForUpdate validates phone number specifically for update operations
func ValidatePhoneNumberForUpdate(phoneNumber, phoneCountryCode, countryCode string) *PhoneValidationResult {
	// Use country code if provided, otherwise use default
	region := "PK" // Default
	if countryCode != "" {
		region = countryCode
	}
	
	result := ValidatePhoneNumberFormat(phoneNumber, region)
	
	// Additional validation for consistency
	if result.IsValid && phoneCountryCode != "" {
		if result.CountryCode != phoneCountryCode {
			result.IsValid = false
			result.ErrorMessage = "Phone country code does not match the provided phone number"
		}
	}
	
	return result
}
