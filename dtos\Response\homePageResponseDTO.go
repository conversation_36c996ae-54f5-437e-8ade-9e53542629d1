package ResponseDTO

// import "time"

// type HomePageResponseDTO struct {
// 	APIResponse
// 	Data Data
// }

// type Data struct {
// 	Balance            float64              `json:"balance"`
// 	TransactionHistory []RecentTransactions `json:"transaction_history"`
// }

type HomePageResponseDTO struct {
	APIResponse
	Data Data
}
type Data struct {
	Balance            float64              `json:"balance"`
	TransactionHistory []RecentTransactions `json:"transaction_history"`
	Notifications      Notification         `json:"notifications"` // Add this field
}
type Notification struct {
	Type    string `json:"type"` // payment_received, topup, wallet_transfer, beneficiary_added, kyc_verified
	Title   string `json:"title"`
	Message string `json:"message"`
	IsRead  bool   `json:"is_read"`
	//Data      map[string]interface{} `json:"data,omitempty"`
	//CreatedAt time.Time              `json:"created_at"`
}
