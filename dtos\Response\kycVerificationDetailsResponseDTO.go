package ResponseDTO

import "time"

type KYCVerificationDetailsResponse struct {
	APIResponse
	Details KYCVerificationDetailsDTO `json:"details"`
}

type KYCVerificationDetailsDTO struct {
	Email            string    `json:"email"`
	PhoneNumber      string    `json:"phone_number"`
	NicURLFront      string    `json:"nic_url_front"`
	NicURLBack       string    `json:"nic_url_back"`
	UserPhotoURL     string    `json:"user_photo_url"`
	VerificationID   uint      `json:"verification_id"`
	FullName         string    `json:"full_name"`
	RequestDate      time.Time `json:"request_date"`
	WabisabiID       string    `json:"wabisabi_id"`
	VerificationType string    `json:"verification_type"`
}
