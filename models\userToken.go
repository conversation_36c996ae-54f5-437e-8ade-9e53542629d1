package models

import "time"

type UserToken struct {
	Id           uint  	 	`gorm:"primaryKey" json:"id"`
	TokenTypeID  uint   	`gorm:"not null" json:"token_type_id"`
	UserID       uint   	`gorm:"not null" json:"user_id"`
	AccessToken  string 	`gorm:"not null;size:255"`
	RefreshToken *string
	IsExpired    bool      	`gorm:"default:false" json:"is_expired"`
	ItemID       *string   	`gorm:"unique;size:255" json:"item_id"` // Single ItemID per Institution
	CreatedAt    time.Time 	`json:"created_at"`
	UpdatedAt    time.Time 	`json:"updated_at"`
	ExpiredAt    time.Time 	`json:"expired_at"`
	// Relations
	TokenType TokenType		`gorm:"foreignKey:TokenTypeID" json:"token_type"`
	User      User      	`gorm:"foreignKey:UserID;references:id"`
}
