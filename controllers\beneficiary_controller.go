package controllers

import (
	"auth-service/services"

	"github.com/labstack/echo/v4"
)

func AddBeneficiary(c echo.Context) error {
	return services.AddBeneficiary(c)
}

func GetBeneficiaries(c echo.Context) error {
	return services.GetBeneficiaries(c)
}

func GetBeneficiaryDetails(c echo.Context) error {
	return services.GetBeneficiaryDetails(c)
}

func GetBeneficiaryTypes(c echo.Context) error {
	return services.GetBeneficiaryTypes(c)
}

func UpdateBeneficiaryType(c echo.Context) error {
	return services.UpdateBeneficiaryType(c)
}
func UpdateFavBeneficiaryType(c echo.Context) error {
	return services.UpdateFavBeneficiaryType(c)
}
func DeleteBeneficiary(c echo.Context) error {
	return services.DeleteBeneficiary(c)
}

func SearchBeneficiary(c echo.Context) error {
	return services.SearchBeneficiary(c, false)
}

func SearchFavBeneficiary(c echo.Context) error {
	return services.SearchBeneficiary(c, true)
}

func GetFavBeneficiaries(c echo.Context) error {
	return services.GetFavBeneficiaries(c)
}