package services

import (
	"auth-service/config"
	resDTO "auth-service/dtos/Response"
	"auth-service/models"
	"encoding/json"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
)

// SaveNotification saves a notification to the database
func SaveNotification(userID uint, notificationType string, title string, message string, data map[string]interface{}) error {
	// Convert data map to JSON string
	dataJSON, err := json.Marshal(data)
	if err != nil {
		return err
	}

	notification := models.Notification{
		UserID:    userID,
		Type:      notificationType,
		Title:     title,
		Message:   message,
		Data:      string(dataJSON),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	return config.DB.Create(&notification).Error
}

func GetNotifications(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			IsSuccess: false,
			Message:   "Error converting user ID",
		})
	}

	// Get pagination parameters
	page, _ := strconv.Atoi(c.QueryParam("page"))
	if page < 1 {
		page = 1
	}

	limit, _ := strconv.Atoi(c.QueryParam("limit"))
	if limit < 1 {
		limit = 10
	}

	// Get notifications
	notifications, total, err := GetUserNotifications(userID, page, limit)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			IsSuccess: false,
			Message:   "Error fetching notifications",
		})
	}

	return c.JSON(http.StatusOK, resDTO.NotificationListResponseDTO{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "Notifications fetched successfully",
		},
		Data: resDTO.NotificationListData{
			Notifications: notifications,
			Total:         total,
			Page:          page,
			Limit:         limit,
		},
	})
}
