package ResponseDTO

import "time"

type OnfidoApplicant struct {
	ID        string    `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	Sandbox   bool      `json:"sandbox"`

	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Email     string `json:"email"`
	DOB       string `json:"dob"`

	DeleteAt *time.Time `json:"delete_at,omitempty"`
	Href     string     `json:"href"`

	IDNumbers   []interface{} `json:"id_numbers"` // Can replace with specific struct if needed
	PhoneNumber string        `json:"phone_number"`

	Address struct {
		FlatNumber     *string `json:"flat_number"`
		BuildingNumber *string `json:"building_number"`
		BuildingName   *string `json:"building_name"`
		Street         string  `json:"street"`
		SubStreet      *string `json:"sub_street"`
		Town           string  `json:"town"`
		State          *string `json:"state"`
		Postcode       string  `json:"postcode"`
		Country        string  `json:"country"`
		Line1          *string `json:"line1"`
		Line2          *string `json:"line2"`
		Line3          *string `json:"line3"`
	} `json:"address"`

	Location struct {
		IPAddress          string `json:"ip_address"`
		CountryOfResidence string `json:"country_of_residence"`
	} `json:"location"`
}
