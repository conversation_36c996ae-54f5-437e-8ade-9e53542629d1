package config

import (
	"auth-service/dtos"
	"fmt"
	"log"

	"github.com/spf13/viper"
)

var ActiveProfile string = "dev"
var ApiVersion string = "v1.0"

func GetEnvFile() *dtos.Config {
	config, errEnv := GetEnvConfig(".")
	if errEnv != nil {
		log.Fatal("ERROR | Error in Loading Environment Variables File")
	}
	return &config
}

func GetEnvConfig(path string) (config dtos.Config, err error) {
	//os.Setenv("Active_Profile", ActiveProfile)
	fmt.Println("Active Profile: ", ActiveProfile)
	viper.AddConfigPath(path)
	viper.SetConfigName(ActiveProfile)
	viper.SetConfigType("env")
	viper.AutomaticEnv()

	err = viper.ReadInConfig()
	if err != nil {
		return
	}

	err = viper.Unmarshal(&config)
	return
}
