package ResponseDTO

import (
	"time"
)

type AdminLoginResponse struct {
	APIResponse
	Token       string   `json:"token"`
	Role        string   `json:"role"`
	Permissions []string `json:"permissions"`
	User        AdminDTO `json:"user"`
}

type AdminDTO struct {
	ID        uint      `json:"id"`
	Email     string    `json:"email"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Role      string    `json:"role"`
	LastLogin time.Time `json:"last_login"`
}
