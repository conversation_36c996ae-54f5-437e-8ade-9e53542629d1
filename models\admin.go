package models

import "time"

type Admin struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	Email       string    `gorm:"size:100;not null;unique" json:"email"`
	Password    string    `gorm:"size:255;not null" json:"-"` // Hashed password
	FirstName   string    `gorm:"size:100;not null" json:"first_name"`
	LastName    string    `gorm:"size:100;not null" json:"last_name"`
	Role        string    `gorm:"size:50;not null;default:'admin'" json:"role"` // admin, super_admin
	Permissions string    `gorm:"type:text" json:"permissions"`                 // JSON string of permissions
	LastLogin   time.Time `json:"last_login"`
	CreatedAt   time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt   time.Time `gorm:"autoUpdateTime" json:"updated_at"`
}
