package services

import (
	cfg "auth-service/config"
	"auth-service/models"
	"time"

	"gorm.io/gorm"
)

// Define a common interface that both OAuth and Plaid can implement
type TokenData interface {
	GetAccessToken() string
	GetRefreshToken() string
	GetExpiredAt() time.Time
	GetUserId() uint
	GetItemId() string
	GetProvider() string
}

type OAuthToken struct {
	UserId       uint
	AccessToken  string
	RefreshToken *string
	ExpiredAt    time.Time
	Provider     string
}

func (o *OAuthToken) GetAccessToken() string {
	return o.AccessToken
}

func (o *OAuthToken) GetRefreshToken() string {
	return *o.RefreshToken
}

func (o *OAuthToken) GetExpiredAt() time.Time {
	return o.ExpiredAt
}
func (o *OAuthToken) GetProvider() string {
	return o.Provider
}

func (o *OAuthToken) GetUserId() uint {
	return o.UserId
}

func (o *OAuthToken) GetItemId() string {
	return ""
}

type PlaidToken struct {
	UserId      uint
	AccessToken string
	ExpiredAt   time.Time
	Provider    string
	ItemId      *string
}

func (p *PlaidToken) GetAccessToken() string {
	return p.AccessToken
}

func (p *PlaidToken) GetRefreshToken() string {
	// Plaid doesn't use refresh token
	return ""
}

func (p *PlaidToken) GetExpiredAt() time.Time {
	return p.ExpiredAt
}

func (p *PlaidToken) GetProvider() string {
	return p.Provider
}

func (p *PlaidToken) GetUserId() uint {
	return p.UserId
}
func (p *PlaidToken) GetItemId() string {
	return *p.ItemId
}

// Your function now accepts the TokenData interface instead of specific parameters
func SaveToken(tokenData TokenData) error {
	// Step 1: Get or Create TokenType
	var tokenType models.TokenType

	if err := cfg.DB.Where("token_type = ?", tokenData.GetProvider()).First(&tokenType).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			tokenType = models.TokenType{TokenType: tokenData.GetProvider()}
			if err := cfg.DB.Create(&tokenType).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	}

	// Step 2: Check if a UserToken exists
	var userToken models.UserToken
	if err := cfg.DB.Where("user_id = ? AND token_type_id = ?", tokenData.GetUserId(), tokenType.Id).First(&userToken).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			// Step 3: Create new UserToken
			userToken = models.UserToken{
				UserID:       tokenData.GetUserId(),
				TokenTypeID:  tokenType.Id,
				IsExpired:    false,
				ExpiredAt:    tokenData.GetExpiredAt(),
				AccessToken:  tokenData.GetAccessToken(),
				RefreshToken: checkNullString(tokenData.GetRefreshToken()),
				ItemID:       checkNullString(tokenData.GetItemId()),
			}
			if err := cfg.DB.Create(&userToken).Error; err != nil {
				return err
			}
		} else {
			return err
		}
	} else {
		userToken = models.UserToken{
			IsExpired:    false,
			ExpiredAt:    tokenData.GetExpiredAt(),
			AccessToken:  tokenData.GetAccessToken(),
			RefreshToken: checkNullString(tokenData.GetRefreshToken()),
		}
		err := cfg.DB.
			Where("user_id = ? and  token_type_id = ?", tokenData.GetUserId(), tokenType.Id).
			Updates(userToken).Error
		if err != nil {
			return err
		}
	}
	return nil
}
