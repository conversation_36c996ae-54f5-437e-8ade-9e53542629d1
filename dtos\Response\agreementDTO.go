package ResponseDTO

import "time"

type AgreementResponseDTO struct {
	MessageID   uint            `json:"message_id"`
	Title       string          `json:"title"`
	Description string          `json:"description"`
	Status      string          `json:"status"`
	CreatedAt   time.Time       `json:"created_at"`
	Users       []AgreementUser `json:"users"`
	Milestones  []MilestoneDTO  `json:"milestones,omitempty"`
}

type AgreementUser struct {
	UserID    uint      `json:"user_id"`
	FirstName string    `json:"first_name"`
	LastName  string    `json:"last_name"`
	Role      string    `json:"role"`
	Status    string    `json:"status"`
	JoinedAt  time.Time `json:"joined_at,omitempty"`
}