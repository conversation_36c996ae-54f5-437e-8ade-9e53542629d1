package ResponseDTO

type BeneficiaryDetailsResponse struct {
	APIResponse
	BeneficiaryDetails *BeneficiaryDetails `json:"beneficiary_details"`
}

type BeneficiaryDetails struct {
	BeneficiaryID uint   `json:"beneficiary_id"`
	Name          string `json:"beneficiary_name"`
	WSID          string `json:"ws_id"`
	WalletID      string `json:"wallet_id"`
	CreatedAt     string `json:"created_at"`
	IsFav		  bool   `json:"is_fav"`
}

type BeneficiaryDetailsResponseArr struct {
	APIResponse
	BeneficiaryDetails []BeneficiaryDetails `json:"beneficiaries"`
}