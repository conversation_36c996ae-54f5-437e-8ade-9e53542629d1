package services

import (
	resDTO "auth-service/dtos/Response"
	"fmt"
	"net/http"

	"github.com/labstack/echo/v4"
	"github.com/markbates/goth/gothic"
	"gorm.io/gorm"
)

func AuthHandler(c echo.Context) error {
	provider := c.<PERSON>m("provider")
	print(provider)

	q := c.Request().URL.Query()
	q.Add("provider", provider)
	c.Request().URL.RawQuery = q.Encode()

	req := c.Request()
	res := c.Response().Writer

	if gothUser, err := gothic.CompleteUserAuth(res, req); err == nil {
		return c.JSON(http.StatusOK, gothUser)
	}

	gothic.BeginAuthHandler(c.Response(), c.Request())
	
	return nil
}

// Updated handler using the separated DB function
func AuthCallbackHandler(c echo.Context) error {
	user, err := gothic.CompleteUserAuth(c.Response(), c.Request())
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "OAuth authentication failed"})
	}

	dbUser, err := FindUserByEmail(user.Email)
	if err == gorm.ErrRecordNotFound {
		dbUser, err = CreateUserByOAuth(user)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to create user"})
		}
	} else if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Database error"})
	}

	refToken := user.RefreshToken
	oauthToken := &OAuthToken{
		AccessToken:  user.AccessToken,
		ExpiredAt:    user.ExpiresAt,
		UserId:       dbUser.Id,
		Provider:     user.Provider,
		RefreshToken: &refToken,
	}

	err = SaveToken(oauthToken)
	if err != nil {
		fmt.Println("Error saving Plaid token:", err)
	}

	tokenString, err := GenerateJWT(dbUser.Email, dbUser.Id)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to generate JWT"})
	}

	return c.JSON(http.StatusOK, resDTO.OAuthResponseDTO{
		APIResponse: resDTO.APIResponse{IsSuccess: true, Message: "Successfully authenticated"},
		Token:       tokenString,
	})
}