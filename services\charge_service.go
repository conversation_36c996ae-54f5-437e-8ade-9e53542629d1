package services

import (
	"auth-service/config"
	resDTO "auth-service/dtos/Response"
	"auth-service/models"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
)

// GetCharges retrieves all active charges
func GetCharges(c echo.Context) error {
	var charges []models.Charge
	if err := config.DB.Where("is_active = ?", true).Find(&charges).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Error fetching charges",
		})
	}

	var chargeDTOs []resDTO.ChargeDTO
	for _, charge := range charges {
		chargeDTOs = append(chargeDTOs, resDTO.ChargeDTO{
			ID:          charge.ID,
			Name:        charge.Name,
			Description: charge.Description,
			Amount:      charge.Amount,
			Currency:    charge.Currency,
			IsActive:    charge.IsActive,
			CreatedAt:   charge.CreatedAt.Format(time.RFC3339),
			UpdatedAt:   charge.UpdatedAt.Format(time.RFC3339),
		})
	}

	return c.J<PERSON>(http.StatusOK, resDTO.ChargesResponseDTO{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "Charges retrieved successfully",
		},
		Data: chargeDTOs,
	})
}

// GetChargeByType retrieves a charge by its type
func GetChargeByType(chargeType string) (*models.Charge, error) {
	var charge models.Charge
	if err := config.DB.Where("name = ? AND is_active = ?", chargeType, true).First(&charge).Error; err != nil {
		return nil, err
	}
	return &charge, nil
}
