package router

import (
	"auth-service/controllers"
	"auth-service/middlewares"
	"auth-service/services"
	"os"

	"github.com/labstack/echo/v4"
)

// SetupRoutes Define all routes in a structured way
func SetupRoutes(e *echo.Group) {
	e.St<PERSON>("/", os.<PERSON>env("STATIC_DIR"))
	e.POST("/webhook", controllers.StripeWebhook)

	// Define OAuth routes
	auth := e.Group("/auth")
	{
		auth.GET("/:provider", controllers.AuthHandler)
		auth.GET("/:provider/callback", services.AuthCallbackHandler)
	}

	// Public routes with logging only
	public := e.Group("", middlewares.LoggingMiddleware)
	{
		public.POST("/auth/validateEmail", controllers.ValidateEmail)
		public.POST("/validatePhoneNumber", controllers.ValidatePhoneNumber)
		public.POST("/validateWSID", controllers.ValidateWSID)
		public.POST("/auth/register", controllers.RegisterUser)
		public.POST("/auth/login", controllers.Login)
		public.POST("/verifyOTP", controllers.VerifyOTP)
		public.POST("/resendOTP", controllers.ResendOTP)
		public.POST("/sendOTP", controllers.SendOTP)
		public.POST("/forgetPassword", controllers.ForgetPassword)
		public.POST("/resetPassword", controllers.ResetPassword)
		public.GET("/charges", controllers.GetCharges)
	}

	// Protected routes with authentication and logging
	api := e.Group("/ws", middlewares.AuthMiddleware, middlewares.LoggingMiddleware, middlewares.ErrorLogger("ws-service"))
	{
		api.GET("/homepage", services.WebSocketConnection)
		api.POST("/validatePin", controllers.ValidatePinCode)
		api.PATCH("/setIdPin", controllers.SetCredentials)
		api.PATCH("/updatePhoneNumber", controllers.UpdatePhoneNumber)
		api.PATCH("/setPin", controllers.SetPinCode)
		api.PATCH("/changePin", controllers.ChangePinCode)
		api.PATCH("/userDetails", controllers.InsertUserDetails)
		api.POST("/changePassword", controllers.ChangePassword)
		api.POST("/validateIban", controllers.ValidateIban)
		api.POST("/setBankDetails", controllers.SetBankDetails)
		api.GET("/getQuestions", controllers.GetQuestions)
		api.POST("/insertQuesAttempt", controllers.QuestionsAttempt)

		api.POST("/addBeneficiary", controllers.AddBeneficiary)
		api.GET("/getBeneficiary", controllers.GetBeneficiaries)
		api.GET("/getFavBeneficiaries", controllers.GetFavBeneficiaries)
		api.GET("/getBeneficiaryDetails/:b_id", controllers.GetBeneficiaryDetails)
		api.DELETE("/deleteBeneficiary", controllers.DeleteBeneficiary)
		api.GET("/beneficiaryTypes", controllers.GetBeneficiaryTypes)
		api.PATCH("/updateBeneficiaryType", controllers.UpdateBeneficiaryType)
		api.PATCH("/updateFavBeneficiaryType", controllers.UpdateFavBeneficiaryType)
		api.GET("/searchBeneficiary", controllers.SearchBeneficiary)
		api.GET("/searchFavBeneficiary", controllers.SearchFavBeneficiary)
		api.GET("/getUserDetails", controllers.GetUserDetails)
		api.GET("/getUserDetailsByToken", controllers.GetUserDetailsByToken)

		api.GET("/liveChatSocket", controllers.ChatController)
		api.GET("/bgSocket", controllers.BackgroundController)
		api.GET("/homePage", controllers.GetHomePage)
		api.GET("/listConversations", controllers.GetConversationList)
		api.GET("/fullConversation", controllers.GetFullConversation)

		api.GET("/graph", controllers.GetGraphValues)
		api.GET("/graph/keys", controllers.GetGraphKeys)

		//api.POST("/storeNic", controllers.StoreNIC)
	}

	txn := e.Group("/txn", middlewares.AuthMiddleware, middlewares.LoggingMiddleware)
	{
		txn.GET("/paymentPurpose", controllers.GetPaymentTypes)

		txn.POST("/transfer/wallet", controllers.TransferFundsWallet)
		txn.GET("/transfer/history", controllers.TransferHistory)
		txn.GET("/balance", controllers.ViewBalance)
	}

	qr := e.Group("/qr", middlewares.AuthMiddleware, middlewares.LoggingMiddleware)
	{
		qr.GET("/generate", controllers.QRGenerator)
	}

	plaid := e.Group("/pld", middlewares.AuthMiddleware, middlewares.LoggingMiddleware)
	{
		plaid.GET("/getBanks", controllers.GetAllBanks)
		plaid.GET("/getLinkToken", controllers.CreatePlaidTokenUser)
		plaid.POST("/exchangePublicToken", controllers.ExchangePublicToken)
	}

	stripe := e.Group("/stripe", middlewares.AuthMiddleware, middlewares.LoggingMiddleware, middlewares.ErrorLogger("stripe-service"))
	{
		stripe.GET("/createStripeCustomer", controllers.CreateStripeCustomer)
		stripe.POST("/createConnectedAccount", controllers.CreateConnectedAccount)
		stripe.POST("/createPaymentMethods", controllers.CreatePaymentMethods)
		stripe.POST("/addCardToCustomer", controllers.AddCardToCustomer)
		stripe.GET("/getClientSecret", controllers.GetClientSecret)
		stripe.POST("/topup", controllers.TopupStripe)
		stripe.GET("/customerKYC", controllers.CreateKYCVerificationSession)
		stripe.GET("/getKYCStatus", controllers.GetKYCStatus)
		stripe.GET("/getUserSavedCards", controllers.GetUserSavedCards)
		stripe.PATCH("/setDefaultCard", controllers.SetDefaultCard)
		stripe.DELETE("/deleteCard", controllers.DeleteCard)
	}

	// webhook URL: https://localhost:8081/api/onfido/webhooks
	onfido := e.Group("/onfido", middlewares.AuthMiddleware, middlewares.LoggingMiddleware)
	{
		onfido.GET("/getApplicantId", controllers.CreateOnfidoApplicant)
		onfido.POST("/getSDKToken", controllers.CreateWorkflowRun)
		onfido.POST("/webhooks", controllers.OnfidoWebhookHandler)
	}

	e.POST("/contacts", controllers.ContactController, middlewares.AuthMiddleware)

	admin := e.Group("/admin", middlewares.AdminCORS())
	{
		admin.POST("/login", controllers.AdminLogin)

		// Protected admin routes
		protectedAdmin := admin.Group("", middlewares.AdminMiddleware, middlewares.LoggingMiddleware, middlewares.AdminCORS())
		{
			protectedAdmin.POST("/create", controllers.CreateAdmin)
			protectedAdmin.GET("/kyc/pending", controllers.GetPendingKYCVerifications)
			protectedAdmin.GET("/kyc/details", controllers.GetKYCVerificationDetails)
			protectedAdmin.PATCH("/updateAdminDetails", controllers.UpdateAdminDetails)
			protectedAdmin.PATCH("/kycVerificationsAction", controllers.KycAction)

		}

	}

	notifications := e.Group("/notification", middlewares.AuthMiddleware, middlewares.LoggingMiddleware)
	{
		notifications.GET("/getNoitification", controllers.GetNotifications)
	}
}
