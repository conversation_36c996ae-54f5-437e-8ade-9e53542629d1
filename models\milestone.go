package models

import (
	"time"

	"gorm.io/gorm"
)

type Milestones struct {
	gorm.Model

	ProjectID    uint       `gorm:"not null" json:"project_id"`
	Title        string     `gorm:"size:255;not null" json:"title"`
	Description  *string    `gorm:"type:text" json:"description"`
	DueDate      *time.Time `gorm:"type:date" json:"due_date"`
	Amount       float64    `gorm:"not null" json:"amount"`
	Status       string     `gorm:"size:50;default:'pending'" json:"status"`
	ContractHash *string    `gorm:"type:text" json:"contract_hash"`

	Project Projects `gorm:"foreignKey:ProjectID;references:ID;constraint:OnDelete:CASCADE" json:"project"`
}
