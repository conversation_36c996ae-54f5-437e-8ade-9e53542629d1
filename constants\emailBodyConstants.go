package constants

import "fmt"

//TODO: INSERT ALL BODY CONSTANTS in static_configutation table with code as key and value as body

func OTPBody(otp string) string {
	return fmt.Sprintf(`Dear Customer,

Your WabiSabi Pay verification code is: %s

Please use this OTP to complete your verification. Do not share this code with anyone.

Thank you,
WabiSabi Pay Team`, otp)
}

func TopupBody(amount float64, card string) string {
	return fmt.Sprintf(`Dear Customer,

Your top-up of %f USD via %s has been initiated successfully. It will reflect in your wallet shortly.

Thank you,
WabiSabi Pay Team`, amount, card)
}

func WalletToWalletBody(amount, toUser string) string {
	return fmt.Sprintf(`Dear Customer,

You have successfully transferred %s USD to %s using your WabiSabi Pay wallet.

Thank you,
WabiSabi Pay Team`, amount, toUser)
}

func AddBeneficiaryBody(name, typeLabel string) string {
	return fmt.Sprintf(`Dear Customer,

You have successfully added a new beneficiary:
Name: %s
Type: %s

You can now send money easily to this beneficiary.

Thank you,
WabiSabi Pay Team`, name, typeLabel)
}
func ForgotPasswordOTPBody(otp string) string {
	return fmt.Sprintf(`Dear Customer,

We received a request to reset your WabiSabi Pay password.

Your One-Time Password (OTP) is: %s

Please enter this code in the app to proceed with resetting your password. Do not share this code with anyone.

If you did not request a password reset, please ignore this email or contact our support team.

Thank you,
WabiSabi Pay Team`, otp)
}
