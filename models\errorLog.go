package models

import (
	"time"
)

type ErrorLog struct {
	ID          uint      `gorm:"primaryKey"`
	ServiceName string    `gorm:"type:varchar(100);not null"`
	FileName    string    `gorm:"type:varchar(255);not null"`
	LineNumber  int       `gorm:"not null"`
	Error       string    `gorm:"type:text;not null"`
	Stack       string    `gorm:"type:text"`
	UserID      *uint     `gorm:"index"`
	RequestID   string    `gorm:"type:varchar(100)"`
	CreatedAt   time.Time `gorm:"not null"`
}
