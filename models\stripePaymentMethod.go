package models

import "time"

type StripePaymentMethod struct {
	ID               uint      `gorm:"primaryKey;autoIncrement;" json:"id"`
	UserID           uint      `gorm:"not null" json:"user_id"`
	CardUserName     string    `json:"card_user_name"`
	StripeCustomerID string    `gorm:"not null;size:255" json:"stripe_customer_id"`
	MethodType       string    `gorm:"size:255;not null" json:"method_type"`
	MethodToken      string    `gorm:"size:255;not null" json:"method_token"`
	Brand            string    `json:"brand"`
	Last4            string    `json:"last4"`
	ExpMonth         int       `json:"exp_month"`
	ExpYear          int       `json:"exp_year"`
	IsDefault        bool      `json:"is_default"`
	CardIcon         string    `json:"card_icon"`
	CreatedAt        time.Time `json:"created_at"`

	// Associations (both to the same User)
	UserByID       User `gorm:"foreignKey:UserID;references:id;constraint:OnDelete:CASCADE"`
	UserByStripeID User `gorm:"foreignKey:StripeCustomerID;references:StripeCustomerId;constraint:OnDelete:CASCADE"`
}
