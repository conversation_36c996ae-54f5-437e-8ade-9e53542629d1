package models

import "gorm.io/gorm"

type NicDetails struct {
	UserID      uint   `gorm:"not null;constraint:OnDelete:CASCADE;foreignKey:UserID;references:ID;unique"`
	Nic         string `gorm:"size:50;unique" json:"nic"`
	DateOfBirth string `gorm:"size:50" json:"date_of_birth"` // Stored as string (YYYY-MM-DD)
	FullName    string `gorm:"size:100" json:"full_name"`
	User        User   `gorm:"foreignKey:UserID;references:id"`

	gorm.Model
}
