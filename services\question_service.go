package services

import (
	"auth-service/config"
	resDTO "auth-service/dtos/Response"
	"auth-service/models"
	"net/http"

	"github.com/labstack/echo/v4"
)

func GetQuestions(c echo.Context) error {
	c.Set("serviceName", "question_service")
	c.Set("moduleName", "services")
	message := "Questions fetched from database"
	// Get the questions from the database
	ques, err := GetQuestionsFromDatabase(config.DB)
	if err != nil {
		message = "Error getting questions from database"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}
	response := ques
	// resDTO.QuestionResponse{APIResponse: resDTO.APIResponse{IsSuccess: true, Message: "Questions fetched successfully"},
	// 	Question: ques}
	return c.JSON(http.StatusOK, response)

}

func QuestionsAttempt(c echo.Context) error {
	userid, err := InterfaceToUint(c.Get("user_id"))
	message := "Questions Attempted Inserted"

	if err != nil {
		message = "Cannot convert userid"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}

	var req []models.QuestionsAttempt
	err1 := c.Bind(&req)
	if err1 != nil {
		message = "Invalid Request"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message})
	}

	for i := range req {
		req[i].UserID = userid
	}

	err2 := InsertOrUpdateQuestionsAttempt(config.DB, req)
	if err2 != nil {
		message = err2.Error()
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}

	c.Set("customMessage", message)
	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
}
