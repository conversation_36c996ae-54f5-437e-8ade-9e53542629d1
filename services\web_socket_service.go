package services

// Add to ws-service/services/websocket_service.go
import (
	"auth-service/config"
	resDTO "auth-service/dtos/Response"
	"auth-service/models"
	"encoding/json"
	"net/http"
	"strconv"
	"sync"

	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

var (
	upgrader = websocket.Upgrader{
		ReadBufferSize:  1024,
		WriteBufferSize: 1024,
		CheckOrigin: func(r *http.Request) bool {
			return true // Configure this based on your security requirements
		},
	}

	// Map to store active connections
	clients      = make(map[uint]*websocket.Conn)
	clientsMutex sync.RWMutex
)

// WebSocketConnection handles the WebSocket connection
func WebSocketConnection(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error converting user ID"})
	}

	// Upgrade HTTP connection to WebSocket
	ws, err := upgrader.Upgrade(c.Response().Writer, c.Request(), nil)
	if err != nil {
		return err
	}
	defer ws.Close()

	// Store the connection
	clientsMutex.Lock()
	clients[userID] = ws
	clientsMutex.Unlock()
	// Get initial data
	initialData, err := getInitialHomePageData(userID)
	if err != nil {
		return err
	}

	// Send initial data immediately after connection
	if err := ws.WriteJSON(initialData); err != nil {
		return err
	}

	// Remove connection when client disconnects
	defer func() {
		clientsMutex.Lock()
		delete(clients, userID)
		clientsMutex.Unlock()
	}()

	// Keep connection alive and handle incoming messages
	for {
		_, _, err := ws.ReadMessage()
		if err != nil {
			break
		}
	}

	return nil
}

// BroadcastUpdate sends updates to specific user
func BroadcastUpdate(userID uint, data interface{}) {
	clientsMutex.RLock()
	ws, exists := clients[userID]
	clientsMutex.RUnlock()

	if exists {
		ws.WriteJSON(data)
	}
}

// Helper function to get initial homepage data
// func getInitialHomePageData(userID uint) (resDTO.HomePageResponseDTO, error) {
// 	// Get wallet balance
// 	wallet, err := GetWalletAmountSender(config.DB, userID)
// 	if err != nil {
// 		return resDTO.HomePageResponseDTO{}, err
// 	}

// 	walletBalanceStr, err := DecryptAES(wallet.Amount)
// 	if err != nil {
// 		return resDTO.HomePageResponseDTO{}, err
// 	}

// 	balance, err := strconv.ParseFloat(walletBalanceStr, 64)
// 	if err != nil {
// 		return resDTO.HomePageResponseDTO{}, err
// 	}

// 	// Get recent transactions
// 	// var transactionHistory []models.Transaction
// 	// if err := config.DB.Where("user_id = ?", userID).Order("created_at desc").Limit(10).Find(&transactionHistory).Error; err != nil {
// 	// 	return resDTO.HomePageResponseDTO{}, err
// 	// }
// 	var transactionHistory []resDTO.TransactionHistoryDTO
// 	if err := config.DB.Table("transactions t").
// 		Select("t.*, pt.purpose, pt.icon").
// 		Joins("LEFT JOIN payment_types pt ON t.payment_type_id = pt.id").
// 		Where("t.user_id = ?", userID).
// 		Order("t.created_at desc").
// 		Limit(10).
// 		Scan(&transactionHistory).Error; err != nil {
// 		return resDTO.HomePageResponseDTO{}, err
// 	}

// 	var groupedTxn []resDTO.RecentTransactions = make([]resDTO.RecentTransactions, 0)
// 	for _, txn := range transactionHistory {
// 		groupedTxn = append(groupedTxn, resDTO.RecentTransactions{
// 			UserID:   txn.UserID,
// 			WalletID: txn.WalletID,
// 			DrCr:     txn.DrCr,
// 			TxnDate:  txn.TxnDate,
// 			TxnType:  txn.TxnType,
// 			ToFrom:   txn.ToFrom,
// 			Amount:   txn.Amount,
// 			Status:   txn.Status,
// 			Purpose:  txn.Purpose,
// 			Icon:     txn.Icon,
// 		})
// 	}

// 	return resDTO.HomePageResponseDTO{
// 		APIResponse: resDTO.APIResponse{
// 			IsSuccess: true,
// 			Message:   "WebSocket connected successfully",
// 		},
// 		Data: resDTO.Data{
// 			Balance:            balance,
// 			TransactionHistory: groupedTxn,
// 		},
// 	}, nil
// }

func getInitialHomePageData(userID uint) (resDTO.HomePageResponseDTO, error) {
	// Get wallet balance
	wallet, err := GetWalletAmountSender(config.DB, userID)
	if err != nil {
		return resDTO.HomePageResponseDTO{}, err
	}

	walletBalanceStr, err := DecryptAES(wallet.Amount)
	if err != nil {
		return resDTO.HomePageResponseDTO{}, err
	}

	balance, err := strconv.ParseFloat(walletBalanceStr, 64)
	if err != nil {
		return resDTO.HomePageResponseDTO{}, err
	}

	// Get recent transactions
	// var transactionHistory []resDTO.TransactionHistoryDTO
	// if err := config.DB.Table("transactions t").
	// 	Select("t.*, pt.purpose, pt.icon").
	// 	Joins("LEFT JOIN payment_types pt ON t.payment_type_id = pt.id").
	// 	Where("t.user_id = ?", userID).
	// 	Order("t.created_at desc").
	// 	Limit(10).
	// 	Scan(&transactionHistory).Error; err != nil {
	// 	return resDTO.HomePageResponseDTO{}, err
	// }

	var groupedTxn []resDTO.RecentTransactions = make([]resDTO.RecentTransactions, 0)
	groupedTxn = GetRecentTransactions(userID)
	// for _, txn := range transactionHistory {
	// 	groupedTxn = append(groupedTxn, resDTO.RecentTransactions{
	// 		UserID:   txn.UserID,
	// 		WalletID: txn.WalletID,
	// 		DrCr:     txn.DrCr,
	// 		TxnDate:  txn.TxnDate,
	// 		TxnType:  txn.TxnType,
	// 		ToFrom:   txn.ToFrom,
	// 		Amount:   txn.Amount,
	// 		Status:   txn.Status,
	// 		Purpose:  txn.Purpose,
	// 		Icon:     txn.Icon,
	// 	})
	// }

	// Get latest notification
	var latestNotification models.Notification
	if err := config.DB.Where("user_id = ?", userID).
		Order("created_at desc").
		First(&latestNotification).Error; err != nil && err != gorm.ErrRecordNotFound {
		return resDTO.HomePageResponseDTO{}, err
	}

	// Create notifications array
	var notifications resDTO.Notification
	if latestNotification.ID != 0 {
		// Parse the stored JSON data
		var notificationData map[string]interface{}
		if err := json.Unmarshal([]byte(latestNotification.Data), &notificationData); err != nil {
			return resDTO.HomePageResponseDTO{}, err
		}

		notifications = resDTO.Notification{
			Type:    latestNotification.Type,
			Title:   latestNotification.Title,
			Message: latestNotification.Message,
			IsRead:  latestNotification.IsRead,
			// Data:      notificationData,
			// CreatedAt: latestNotification.CreatedAt,
		}
	}

	return resDTO.HomePageResponseDTO{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "WebSocket connected successfully",
		},
		Data: resDTO.Data{
			Balance:            balance,
			TransactionHistory: groupedTxn,
			Notifications:      notifications,
		},
	}, nil
}

// BroadcastNotification sends a notification and saves it to the database
// BroadcastNotification sends a notification and saves it to the database
func BroadcastNotification(userID uint, notificationType string, title string, message string, data map[string]interface{}) error {
	// Save notification to database
	if err := SaveNotification(userID, notificationType, title, message, data); err != nil {
		return err
	}

	// Get current home page data
	homeData, err := getInitialHomePageData(userID)
	if err != nil {
		return err
	}

	// Broadcast the update
	BroadcastUpdate(userID, homeData)
	return nil
}
