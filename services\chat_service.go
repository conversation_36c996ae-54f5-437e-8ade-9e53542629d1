package services

import (
	"auth-service/config"
	reqDTO "auth-service/dtos/Request"
	resDTO "auth-service/dtos/Response"
	"auth-service/models"
	"encoding/json"
	"log"
	"net/http"
	"strconv"

	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
)

// Global connection hubs
var (
	activeHub = NewActiveHub()
	bgHub     = NewBackgroundHub()
)

// Websocket upgrader
var wsUpgrader = websocket.Upgrader{
	ReadBufferSize:  1024,
	WriteBufferSize: 1024,
	CheckOrigin: func(r *http.Request) bool {
		return true // Allow all connections in this example
	},
}

func HandleChat(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{IsSuccess: false, Message: "Error converting user ID"})
	}

	conn, err := wsUpgrader.Upgrade(c.Response(), c.Request(), nil)
	if err != nil {
		return err
	}
	defer conn.Close()

	for {
		_, msgData, err := conn.ReadMessage()
		if err != nil {
			break
		}

		var msg reqDTO.IncomingMessage
		err = json.Unmarshal(msgData, &msg)
		if err != nil {
			continue
		}

		// register our current user
		activeHub.RegisterActiveUser(userID, msg.ReceiverId, conn)
		defer activeHub.UnregisterActiveUser(userID)

		conversationID, err := findOrCreateConversation(userID, msg.ReceiverId)
		if err != nil {
			log.Println("Error finding/creating conversation:", err)
			return err
		}
	
		// Create message record
		message := models.Message{
			ConversationID: conversationID,
			SenderID:       userID,
			Content:        &msg.Message,
			MessageType: 	"text",
		}

		tx := config.DB.Begin()

		// Create the message
		if err := tx.Create(&message).Error; err != nil {
			tx.Rollback()
			log.Println("DB save error:", err)
			continue
		}

		// Handle media if needed
		// if msg.MessageType != "text" && msg.MediaURL != "" {
		// 	media := models.Media{
		// 		MessageID: message.ID,
		// 		MediaType: &msg.MessageType,
		// 		MediaURL:  msg.MediaURL,
		// 	}

		// 	if err := tx.Create(&media).Error; err != nil {
		// 		tx.Rollback()
		// 		log.Println("DB save media error:", err)
		// 		continue
		// 	}
		// }

		// Commit the transaction
		if err := tx.Commit().Error; err != nil {
			log.Println("Transaction commit error:", err)
			continue
		}

		// Check if receiver is online
		receiverBGConn, isReceiverOnlineBg := bgHub.GetBackroundConnection(msg.ReceiverId)

		if isReceiverOnlineBg {
			// Receiver is online in the app
			receiverActiveConn, isReceiverChatting := activeHub.GetActiveConnection(msg.ReceiverId)
			
			unsentMsg := resDTO.UnsentMessageDTO{
				SenderId: userID,
				Message: *message.Content,
				Title: "",
				MessageType: message.MessageType,
				// AgreementId: ,
			}

			if isReceiverChatting && receiverActiveConn.user == userID {
				// is in active chat and talking to current user to send in receiver's active connection
				finalMessage, _ := json.Marshal(unsentMsg)
				err := receiverActiveConn.conn.WriteMessage(websocket.TextMessage, finalMessage)
				if err != nil {
					log.Println("Receiver write error:", err)
				}

			} else {
				// receiver is online but not talking to current user, so send notification
				notificationData, _ := json.Marshal(unsentMsg)
				err := receiverBGConn.WriteMessage(websocket.TextMessage, notificationData)
				if err != nil {
					log.Println("Background notification error:", err)
				}

			}
		}

		// If receiver is not online, message stays in database
	}

	return nil
}

func GetUserConversations(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			IsSuccess: false,
			Message:   "Error converting user ID",
		})
	}

	conversations, err := GetUserConversationsFromDB(userID)
	if err != nil {
		log.Println("DB error fetching conversations:", err)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			IsSuccess: false,
			Message:   "Could not fetch conversations",
		})
	}

	return c.JSON(http.StatusOK, resDTO.ConversationResponseDTO{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "Conversations fetched successfully",
		},
		Result: conversations,
	})
}

func GetFullConversation(c echo.Context) error {
	convoIDStr := c.QueryParam("conversation_id")
	if convoIDStr == "" {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{IsSuccess: false, Message: "conversation_id is required"})
	}

	convoID, err := strconv.ParseUint(convoIDStr, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{IsSuccess: false, Message: "invalid conversation_id"})
	}

	messages := make([]resDTO.FullMessage, 0)

	err = config.DB.Raw(`
		SELECT 
			m.id,
			m.content,
			m.message_type,
			m.sent_at,
			u.id AS sender_id,
			u.first_name AS sender_name,
			me.id AS media_id,
			me.media_type,
			me.media_url
		FROM messages m
		JOIN users u ON m.sender_id = u.id
		LEFT JOIN media me ON m.id = me.message_id
		WHERE m.conversation_id = ?
		ORDER BY m.sent_at ASC`, convoID).Scan(&messages).Error

	if err != nil {
		log.Println("DB error in GetFullConversation:", err)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{IsSuccess: false, Message: "Failed to fetch messages"})
	}

	return c.JSON(http.StatusOK, resDTO.	ConversationDetailsDTO{
		APIResponse: resDTO.APIResponse{IsSuccess: true, Message: "successfully fetched all messages in the conversation"},
		Result: messages,
	})
}

// UpdateBackgroundConnection, supports connection when user opens the app
func UpdateBackgroundConnection(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{IsSuccess: false, Message: "Error converting user ID"})
	}

	conn, err := wsUpgrader.Upgrade(c.Response(), c.Request(), nil)
	if err != nil {
		return err
	}
	defer conn.Close()

	bgHub.RegisterBackgroundUser(userID, conn)
	defer bgHub.UnregisterBackgroundUser(userID)

	// Send any undelivered messages with content and sender info using JOIN
	// type MessageWithContent struct {
	// 	models.Message
	// 	Content           string `json:"content"`
	// 	SenderFirstName   string `json:"sender_first_name"`
	// 	SenderLastName    string `json:"sender_last_name"`
	// 	// SenderProfilePhoto string `json:"sender_profile_photo"`
	// }

	// var undeliveredMessages []MessageWithContent
	// err = config.DB.Table("messages").
	// 	Select("messages.*, COALESCE(text_messages.content, '') as content, users.first_name as sender_first_name, users.last_name as sender_last_name").
	// 	Where("messages.receiver_id = ? AND messages.status = ?", userID, "sent").
	// 	Find(&undeliveredMessages).Error
	
	// if err != nil {
	// 	log.Println("Error fetching undelivered messages:", err)
	// }

	// for _, msg := range undeliveredMessages {
	// 	notificationMsg := resDTO.UnsentMessageDTO{
	// 		SenderId: userID,
	// 		Message: msg.Content,
	// 		Title: "",
	// 		MessageType: msg.MessageType,
	// 		// AgreementId: ,
	// 	}

	// 	notificationData, _ := json.Marshal(notificationMsg)
	// 	if err := conn.WriteMessage(websocket.TextMessage, notificationData); err != nil {
	// 		log.Println("Error sending undelivered notification:", err)
	// 		continue
	// 	}

	// 	// marking as delivered
	// 	err := config.DB.Model(&models.Message{}).Where("id = ?", msg.ID).Update("status", "delivered").Error
	// 	if err != nil {
	// 		log.Println("Error updating message status to delivered:", err)
	// 	}
	// }

	// Keep connection alive for background updates
	// only the server will send any updates in the background, not client
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			break
		}
	}

	return nil
}