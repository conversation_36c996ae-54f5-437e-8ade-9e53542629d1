package services

import (
	"auth-service/config"
	reqDTO "auth-service/dtos/Request"
	resDTO "auth-service/dtos/Response"
	"auth-service/models"
	"net/http"

	"github.com/labstack/echo/v4"
)

func ForgetPassword(c echo.Context) error {
	var req reqDTO.ForgetPasswordRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Invalid request"})
	}
	user, err := getUserByEmail(config.DB, req.Email)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error getting email from database"})
	}
	if user.Email == "" {
		return c.JSON(http.StatusNotFound, resDTO.APIResponse{Message: "Email not registered"})
	} else {
		otp := generateOTP()
		err := SendOTPF(req.Email, "Your otp code", otp)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error sending OTP"})
		}
		return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: "Email found"})
	}
}

func ChangePassword(c echo.Context) error {
	var req reqDTO.ChangePasswordRequest
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		message := "Error converting user id"
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{IsSuccess: false, Message: "Invalid request"})
	}
	user, err := getUserByID(config.DB, userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{IsSuccess: false, Message: "Error getting email from database"})
	}

	// Compare the old passwords
	if !CheckPasswordHash(req.OldPassword, user.Password) {
		return c.JSON(http.StatusUnauthorized, resDTO.APIResponse{IsSuccess: false, Message: "Old Password does not match"})
	} else {
		err := ValidatePassword(req.NewPassword)
		if err != nil {
			return c.JSON(http.StatusBadRequest, resDTO.APIResponse{IsSuccess: false, Message: err.Error()})
		}
		hashedPassword, err := HashPassword(req.NewPassword)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{IsSuccess: false, Message: "Error hashing password"})
		}
		UpdatePassword(config.DB, user, user.Email, hashedPassword)
		return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: "Password changed successfully"})
	}

}

// Reset Password verify otp will call again
func ResetPassword(c echo.Context) error {
	var req reqDTO.ResetPasswordRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Invalid request"})
	}

	// otpMutex.Lock()
	// otpData, exists := otpStore[req.Email]
	// otpMutex.Unlock()

	// if !exists || otpData.Code != req.OTP {
	// 	return c.JSON(http.StatusUnauthorized, res.APIResponse{Message: "Invalid OTP"})
	// }

	// if time.Now().After(otpData.ExpiresAt) {
	// 	return c.JSON(http.StatusUnauthorized, res.APIResponse{Message: "OTP expired"})
	// }

	// Hash new password
	err := ValidatePassword(req.NewPassword)
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{IsSuccess: false, Message: err.Error()})
	}
	hashedPassword, err := HashPassword(req.NewPassword)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error hashing password"})
	}

	// Update password in DB
	config.DB.Model(&models.User{}).Where("email = ?", req.Email).Update("password", hashedPassword)

	// Remove OTP after reset
	otpMutex.Lock()
	delete(otpStore, req.Email)
	otpMutex.Unlock()

	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: "Password reset successfully."})
}
