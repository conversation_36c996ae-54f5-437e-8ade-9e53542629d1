package models

import "time"

type OAuthToken struct {
	ID           uint      `gorm:"primaryKey"`
	UserID       uint      `gorm:"not null;constraint:OnDelete:CASCADE;foreignKey:UserID;references:ID" json:"user_id"`
	Provider     string    `gorm:"not null;size:255"` // "google", "apple"
	AccessToken  string    `gorm:"not null;size:255"`
	RefreshToken string    `gorm:"not null;size:255"`
	ExpiresAt    time.Time `gorm:"not null"`
	CreatedAt    time.Time
	User         User `gorm:"foreignKey:UserID;references:id"`
}
