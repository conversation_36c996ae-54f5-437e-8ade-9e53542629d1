package controllers

import (
	"auth-service/services"

	"github.com/labstack/echo/v4"
)

func RegisterUser(c echo.Context) error {
	return services.Register(c)
}

func Login(c echo.Context) error {
	return services.LoginUser(c)
}
func GetUserDetailsByToken(c echo.Context) error {
	return services.GetUserDetailsByToken(c)
}

func SetCredentials(c echo.Context) error {
	return services.SetCredentials(c)
}

func SetPinCode(c echo.Context) error {
	return services.SetPinCode(c)
}
func ChangePinCode(c echo.Context) error {
	return services.ChangePinCode(c)
}

func ForgetPassword(c echo.Context) error {
	return services.ForgetPassword(c)
}

func ResetPassword(c echo.Context) error {
	return services.ResetPassword(c)
}

func ChangePassword(c echo.Context) error {
	return services.ChangePassword(c)

}

func ValidateEmail(c echo.Context) error {
	return services.ValidateEmail(c)
}

func ValidateWSID(c echo.Context) error {
	return services.ValidateWSID(c)
}

func ValidatePhoneNumber(c echo.Context) error {
	return services.ValidatePhoneNumber(c)
}
