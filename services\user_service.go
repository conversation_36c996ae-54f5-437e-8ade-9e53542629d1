package services

import (
	"auth-service/config"
	reqDTO "auth-service/dtos/Request"
	resDTO "auth-service/dtos/Response"
	"strconv"

	"auth-service/models"
	"fmt"
	"net/http"

	"github.com/gorilla/websocket"
	"github.com/labstack/echo/v4"
)

func ValidateEmail(c echo.Context) error {
	c.Set("serviceName", "user_service")
	c.Set("moduleName", "services")

	var eReq reqDTO.ValidateEmailRequest
	if err := c.Bind(&eReq); err != nil {
		message := "Invalid Request"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message})
	}

	checkuser, err := getUserByEmail(config.DB, eReq.Email)
	if err != nil {
		message := "Error getting email from database"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	if checkuser.Email != "" {
		message := "Email already exists"
		c.Set("customMessage", message)
		return c.JSON(http.StatusConflict, resDTO.APIResponse{Message: message, IsSuccess: false})
	}
	otp := generateOTP()
	err1 := SendOTPF(eReq.Email, "Validate Email", otp)
	if err1 != nil {
		message := "Error sending OTP"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message, IsSuccess: false})
	}
	message := "Email is valid and OTP is sent"
	c.Set("customMessage", message)
	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
}

func ValidateWSID(c echo.Context) error {
	c.Set("serviceName", "user_service")
	c.Set("moduleName", "services")

	message := "WSID is valid"
	var vReq reqDTO.ValidateWSIDRequest

	//email := c.Get("email")
	err := c.Bind(&vReq)
	if err != nil {
		message := "Invalid Request"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message})
	}

	checkuser, err := getUserByWSID(config.DB, vReq.WSID)
	if err != nil {
		message := "Error getting WSID from database"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	if checkuser.WSID != "" {
		message := "WSID already exists"
		c.Set("customMessage", message)
		return c.JSON(http.StatusConflict, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	c.Set("customMessage", message)
	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
}

func ValidatePhoneNumber(c echo.Context) error {
	c.Set("serviceName", "user_service")
	c.Set("moduleName", "services")

	message := "Phone_number is valid"
	var vReq reqDTO.ValidatePhoneNumber

	//email := c.Get("email")
	err := c.Bind(&vReq)
	if err != nil {
		message := "Invalid Request"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message})
	}

	checkuser, err := GetUserByPhoneNumber(config.DB, vReq.PhoneNumber)
	if err != nil {
		message := "Error getting phone from database"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	if checkuser.PhoneNumber != "" {
		message := "Phone Number already exists"
		c.Set("customMessage", message)
		return c.JSON(http.StatusConflict, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	c.Set("customMessage", message)
	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
}

// TODO: Verify logging structure and remove c.Set wherever used
func Register(c echo.Context) error {
	c.Set("serviceName", "user_service")
	c.Set("moduleName", "services")
	// Bind incoming JSON data to the User struct
	user := new(models.User)
	if err := c.Bind(user); err != nil {
		message := "Invalid Request"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message})
	}

	checkuser, err := getUserByEmail(config.DB, user.Email)
	if err != nil {
		message := "Error getting email from database"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	if checkuser.Email != "" {
		message := "Email already exists"
		c.Set("customMessage", message)
		return c.JSON(http.StatusConflict, resDTO.APIResponse{Message: message, IsSuccess: false})
	} else {
		// Save the user in the database using GORM
		err := ValidatePassword(user.Password)
		if err != nil {
			message := err.Error()
			c.Set("customMessage", message)
			return c.JSON(http.StatusBadRequest, resDTO.APIResponse{IsSuccess: false, Message: message})
		}

		hashedPassword, err1 := HashPassword(user.Password)
		if err1 != nil {
			message := "Error hashing password"
			c.Set("customMessage", message)
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
		}
		user.Password = hashedPassword

		// Hash Pin
		hashedPin, err := HashPassword(user.PIN)
		if err != nil {
			message := "Error updating pin"
			c.Set("customMessage", message)
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
		}
		user.PIN = hashedPin
		userResponse, err := createUser(config.DB, user)
		if err != nil {
			message := "Error saving user"
			c.Set("customMessage", message)
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message, IsSuccess: false})
		}

		// sending otp logic user should save before sending or after sending otp?
		// Send OTP (e.g., via email)
		// err1 := SendOTP(user.Email)
		// if err1 != nil {
		// 	return c.JSON(http.StatusInternalServerError, map[string]interface{}{"Message": "Error sending OTP", "User": user})
		// }
		// Generate JWT token

		token, err := GenerateJWT(userResponse.Email, uint(userResponse.Id))
		if err != nil {
			message := "Error generating token"
			c.Set("customMessage", message)
			return c.JSON(http.StatusInternalServerError, message)
		}

		print(token)
		userDTO := &resDTO.UserResponseDTO{
			// Id: &user.Id,  // Comment this line or set to nil if you don't want `id`
			Email:     user.Email,
			FirstName: user.FirstName,
			LastName:  user.LastName,
			CreatedAt: user.CreatedAt,
			FullName:  user.FirstName + " " + user.LastName,
			Token:     token,
		}
		response := resDTO.RegistrationResponse{APIResponse: resDTO.APIResponse{IsSuccess: true, Message: "User registered successfully"},
			UserData: userDTO}

		go func() {
			_, err := CreateWalletAccount(userResponse.Id)
			if err != nil {
				fmt.Println("Error creating wallet account:", err)
			}
			err1 := SendOTPEmail(user.Email, "User registration email", "registered successfully")
			if err1 != nil {

				fmt.Println("Error sending email:", err)
			}
		}()

		return c.JSON(http.StatusOK, response)
	}
}

func LoginUser(c echo.Context) error {
	c.Set("serviceName", "user_service")
	c.Set("moduleName", "services")
	// Bind incoming JSON data to the User struct
	credentials := new(models.User)
	if err := c.Bind(credentials); err != nil {
		message := "Invalid request"
		c.Set("customMessage", message)
		response := resDTO.APIResponse{Message: message}
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, response)
	}

	// Find the user by email using GORM
	result, err := getUserByEmail(config.DB, credentials.Email)
	if err != nil || result.Email == "" {
		message := "Invalid email or password"
		response := resDTO.APIResponse{Message: message}
		c.Set("customMessage", message)
		return c.JSON(http.StatusUnauthorized, response)
	}

	if result.Provider != "local" && result.Password == "" {
		message := "Please login with google account"
		response := resDTO.APIResponse{Message: message}

		return c.JSON(http.StatusUnauthorized, response)
	}

	// Compare the hashed passwords
	if !CheckPasswordHash(credentials.Password, result.Password) {
		message := "Invalid email or password"
		response := resDTO.APIResponse{Message: message}

		return c.JSON(http.StatusUnauthorized, response)
	}

	// Generate JWT token
	token, err := GenerateJWT(result.Email, uint(result.Id))
	if err != nil {
		message := "Error generating token"
		c.Set("customMessage", message)
		response := resDTO.APIResponse{Message: message}
		return c.JSON(http.StatusInternalServerError, response)
	}

	kycInfo, err := GetUserKYCInfo(config.DB, result.Id)
	if err != nil {
		message := "Error getting KYC info"
		c.Set("customMessage", message)
		response := resDTO.APIResponse{Message: message}

		return c.JSON(http.StatusInternalServerError, response)
	}

	// Set flags based on values
	//isUserDetail := kycInfo.IsVerify
	isKyc := kycInfo.Status

	userDetails, err := getUserDetailsByUserID(config.DB, result.Id)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.UserDetailsResponseDTO{
			APIResponse: resDTO.APIResponse{
				IsSuccess: false,
				Message:   err.Error(),
			},
		})
	}
	//card, err := GetCardDetailsByUserID(config.DB, result.Id)
	card, err := fetchSavedCards(result.Id)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.UserDetailsResponseDTO{
			APIResponse: resDTO.APIResponse{
				IsSuccess: false,
				Message:   "Failed to fetch card details",
			},
		})
	}

	// Find the default card
	var defaultCard *resDTO.CardInfo
	for _, card := range card {
		if card.IsDefault {
			defaultCard = &card
			break
		}
	}
	response := resDTO.LoginResponse{
		APIResponse: resDTO.APIResponse{IsSuccess: true, Message: "Login Successfully"},
		UserData: resDTO.UserData{
			FullName: result.FirstName + " " + result.LastName,
			Token:    token,
			//IsUserDetail:     isUserDetail,
			IsKyc:            isKyc,
			PhoneNumber:      result.PhoneNumber,
			CountryCode:      result.CountryCode,
			PhoneCountryCode: result.PhoneCountryCode,
			WS_id:            result.WSID,
			PaymentMethodID:  kycInfo.PaymentMethodID,
		},
		UserDetails: *userDetails, Card: defaultCard}

	return c.JSON(http.StatusOK, response)

}

func GetUserDetailsByToken(c echo.Context) error {
	userID, _ := InterfaceToUint(c.Get("user_id"))
	// Bind incoming JSON data to the User struct

	// Find the user by email using GORM
	result, err := getUserByID(config.DB, userID)
	if err != nil || result.Email == "" {
		message := "User not Found"
		response := resDTO.APIResponse{Message: message}
		c.Set("customMessage", message)
		return c.JSON(http.StatusUnauthorized, response)
	}

	kycInfo, err := GetUserKYCInfo(config.DB, result.Id)
	if err != nil {
		message := "Error getting KYC info"
		c.Set("customMessage", message)
		response := resDTO.APIResponse{Message: message}

		return c.JSON(http.StatusInternalServerError, response)
	}
	// Set flags based on values
	isUserDetail := kycInfo.IsVerify
	//isKyc := kycInfo.Status == "verified"
	isKyc := kycInfo.Status
	userDetails, err := getUserDetailsByUserID(config.DB, result.Id)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.UserDetailsResponseDTO{
			APIResponse: resDTO.APIResponse{
				IsSuccess: false,
				Message:   err.Error(),
			},
		})
	}
	card, err := fetchSavedCards(result.Id)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.UserDetailsResponseDTO{
			APIResponse: resDTO.APIResponse{
				IsSuccess: false,
				Message:   "Failed to fetch card details",
			},
		})
	}

	// Find the default card
	var defaultCard *resDTO.CardInfo
	for _, card := range card {
		if card.IsDefault {
			defaultCard = &card
			break
		}
	}
	response := resDTO.LoginResponse{
		APIResponse: resDTO.APIResponse{IsSuccess: true, Message: "User Details Fetched Successfully"},
		UserData: resDTO.UserData{
			FullName:         result.FirstName + " " + result.LastName,
			IsUserDetail:     isUserDetail,
			IsKyc:            isKyc,
			PhoneNumber:      result.PhoneNumber,
			CountryCode:      result.CountryCode,
			PhoneCountryCode: result.PhoneCountryCode,
			WS_id:            result.WSID,
			PaymentMethodID:  kycInfo.PaymentMethodID},
		UserDetails: *userDetails,
		Card:        defaultCard,
	}

	return c.JSON(http.StatusOK, response)

}

func UpdatePhoneNumber(c echo.Context) error {
	c.Set("serviceName", "user_service")
	c.Set("moduleName", "services")

	// TODO: Consider implementing rate limiting for phone number updates
	// TODO: Consider requiring OTP verification for phone number changes

	// Safe type assertion for email
	emailInterface := c.Get("email")
	if emailInterface == nil {
		message := "Authentication error: email not found"
		c.Set("customMessage", message)
		return c.JSON(http.StatusUnauthorized, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	email, ok := emailInterface.(string)
	if !ok {
		message := "Authentication error: invalid email format"
		c.Set("customMessage", message)
		return c.JSON(http.StatusUnauthorized, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	// Use specific DTO instead of full User model
	var req reqDTO.UpdatePhoneNumberRequest
	if err := c.Bind(&req); err != nil {
		message := "Invalid request format"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	// Validate phone number is provided and not empty
	if req.PhoneNumber == "" {
		message := "Phone number is required"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	// Validate phone number format
	validationResult := ValidatePhoneNumberForUpdate(req.PhoneNumber, req.PhoneCountryCode, req.CountryCode)
	if !validationResult.IsValid {
		message := validationResult.ErrorMessage
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	// Use the validated and formatted phone number
	req.PhoneNumber = validationResult.FormattedNumber
	if req.PhoneCountryCode == "" {
		req.PhoneCountryCode = validationResult.CountryCode
	}

	// Check if phone number already exists before attempting update
	existingUser, err := GetUserByPhoneNumber(config.DB, req.PhoneNumber)
	if err != nil {
		message := "Error checking phone number availability"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	if existingUser.PhoneNumber != "" {
		message := "Phone Number already exists"
		c.Set("customMessage", message)
		return c.JSON(http.StatusConflict, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	// Create User object with phone-related fields for update
	userUpdate := &models.User{
		PhoneNumber:      req.PhoneNumber,
		PhoneCountryCode: req.PhoneCountryCode,
		CountryCode:      req.CountryCode,
	}

	// Update phone number in DB
	err = UpdateUserContact(config.DB, userUpdate, email)
	if err != nil {
		message := "Error updating phone number"
		if IsDuplicateKeyError(err) {
			message = "Phone Number already exists"
		}

		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message, IsSuccess: false})
	}

	message := "Phone number updated successfully."
	c.Set("customMessage", message)
	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
}

func SetCredentials(c echo.Context) error {
	c.Set("serviceName", "user_service")
	c.Set("moduleName", "services")
	var req models.User
	email := c.Get("email").(string)
	message := "Pin set successfully."

	if err := c.Bind(&req); err != nil {
		message := "Invalid request"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message})
	}

	// Hash new password
	hashedPin, err := HashPassword(req.PIN)
	if err != nil {
		message := "Error hashing password"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}

	// Update password in DB
	// config.DB.Model(&res.User{}).Where("email = ?", req.Email).Update("hashed_password", hashedPassword)
	err1 := UpdateUserCredentials(config.DB, &req, hashedPin, email, req.WSID)
	if err1 != nil {
		message := "Error updating pin"
		c.Set("customMessage", err1.Error())
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}

	c.Set("customMessage", message)
	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
}

func SetPinCode(c echo.Context) error {
	c.Set("serviceName", "user_service")
	c.Set("moduleName", "services")
	message := "Pin set successfully."

	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error converting user ID"})
	}

	var req models.User
	if err := c.Bind(&req); err != nil {
		message := "Invalid request"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message})
	}

	// Hash Pin
	hashedPin, err := HashPassword(req.PIN)
	if err != nil {
		message := "Error updating pin"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}

	err1 := UpdatePinCode(config.DB, &req, hashedPin, userID)
	if err1 != nil {
		message := "Error updating pin"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}

	c.Set("customMessage", message)
	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
}

func ChangePinCode(c echo.Context) error {
	c.Set("serviceName", "user_service")
	c.Set("moduleName", "services")
	message := "Pin Updated successfully."
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		message := "Error converting user id"
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}
	var req reqDTO.ChangePinRequest
	if err := c.Bind(&req); err != nil {
		message := "Invalid request"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message})
	}
	user, err := getUserByID(config.DB, userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{IsSuccess: false, Message: "Error getting email from database"})
	}
	PinMached := CheckPasswordHash(req.OldPin, user.PIN)
	if PinMached {
		// Hash new password
		hashedPin, err := HashPassword(req.NewPin)
		if err != nil {
			message := "Error updating pin"
			c.Set("customMessage", message)
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
		}
		err1 := ChangePin(config.DB, user, hashedPin, userID)
		if err1 != nil {
			message := "Error updating pin"
			c.Set("customMessage", message)
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
		}

		c.Set("customMessage", message)
		return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
	}
	message = "Old Pin does not match"
	c.Set("customMessage", message)
	return c.JSON(http.StatusUnauthorized, resDTO.APIResponse{IsSuccess: false, Message: message})

}

func SetBankDetails(c echo.Context) error {
	c.Set("serviceName", "user_service")
	c.Set("moduleName", "services")

	//TODO:Validate IBAN number first from stripe or plaid
	message := "Bank details updated successfully"

	// Correctly extract user_id from URL param
	userID := c.Get("user_id")
	var req models.BankDetails //map[string]interface{}

	// Bind only provided fields from JSON request body
	if err := c.Bind(&req); err != nil {
		message := "Error invalid request"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message})
	}

	// userF := userID.(float64)
	// req.UserID = uint(userF)
	CUserID, err := InterfaceToUint(userID)
	if err != nil {
		message := "Error converting user id"
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}
	req.UserID = CUserID
	// Update only the provided fields
	if err := InsertBankDetails(&req); err != nil {
		message := "Update Failed"
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}

	c.Set("customMessage", message)
	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
}

// TODO: set response DTO and use func getRecentTransaction
func GetHomePageDetails(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "error converting user id"})
	}

	wallet, err := GetWalletAmountSender(config.DB, userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error fetching wallet amount"})
	}

	walletBalanceStr, err := DecryptAES(wallet.Amount)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, fmt.Errorf("error decrypting user balance"))
	}

	balance, err := strconv.ParseFloat(walletBalanceStr, 64)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, fmt.Errorf("error parsing user balance"))
	}

	// var transactionHistory []models.Transaction
	// if err := config.DB.Where("user_id = ?", userID).Order("created_at desc").Limit(10).Find(&transactionHistory).Error; err != nil {
	// 	return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Could not fetch transaction history"})
	// }
	// var transactionHistory []resDTO.TransactionHistoryDTO
	// if err := config.DB.Table("transactions t").
	// 	Select("t.*, pt.purpose, pt.icon").
	// 	Joins("LEFT JOIN payment_types pt ON t.payment_type_id = pt.id").
	// 	Where("t.user_id = ?", userID).
	// 	Order("t.created_at desc").
	// 	Limit(10).
	// 	Scan(&transactionHistory).Error; err != nil {
	// 	return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Could not fetch transaction history"})
	// }
	var groupedTxn []resDTO.RecentTransactions = make([]resDTO.RecentTransactions, 0)
	groupedTxn = GetRecentTransactions(userID)
	// for _, txn := range transactionHistory {
	// 	groupedTxn = append(groupedTxn, resDTO.RecentTransactions{
	// 		UserID:   txn.UserID,
	// 		WalletID: txn.WalletID,
	// 		DrCr:     txn.DrCr,
	// 		TxnDate:  txn.TxnDate,
	// 		TxnType:  txn.TxnType,
	// 		ToFrom:   txn.ToFrom,
	// 		Amount:   txn.Amount,
	// 		Status:   txn.Status,
	// 		Purpose:  txn.Purpose,
	// 		Icon:     txn.Icon,
	// 	})
	// }
	response := resDTO.HomePageResponseDTO{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "successfully fetched user balance",
		},
		Data: resDTO.Data{
			Balance:            balance,
			TransactionHistory: groupedTxn,
		},
	}

	// Check if it's a WebSocket request
	if websocket.IsWebSocketUpgrade(c.Request()) {
		return c.JSON(http.StatusOK, response)
	}

	return c.JSON(http.StatusOK, response)

}
