package services

import (
	"auth-service/config"
	"auth-service/constants"
	reqDto "auth-service/dtos/Request"
	resDTO "auth-service/dtos/Response"
	resDto "auth-service/dtos/Response"
	"auth-service/models"
	"errors"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/account"
	"github.com/stripe/stripe-go/v72/customer"
	"github.com/stripe/stripe-go/v72/identity/verificationsession"
	"github.com/stripe/stripe-go/v72/paymentintent"
	"github.com/stripe/stripe-go/v72/paymentmethod"
	"github.com/stripe/stripe-go/v72/setupintent"
	"github.com/stripe/stripe-go/v72/transfer"
	"gorm.io/gorm"
	// paymentmethod "github.com/stripe/stripe-go/v72/paymentmethod"
)

// This function creates a Stripe customer
func CreateStripeCustomer(c echo.Context) error {
	// Get the user's ID
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		message := "Error converting user id"
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: message})
	}
	// Create a Stripe customer if not exists
	checkStripeUser, err := CheckStripeCustomer(config.DB, userID)
	if err != nil {
		message := "Error getting email from database"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: message, IsSuccess: false})
	}
	if checkStripeUser.StripeCustomerId == nil {
		params := &stripe.CustomerParams{
			Email: stripe.String(checkStripeUser.Email),
			Name:  stripe.String(checkStripeUser.FirstName + " " + checkStripeUser.LastName),
		}
		params.AddMetadata("wspay_userId", strconv.FormatUint(uint64(userID), 10))
		// Create the customer
		stripeCustomer, err := customer.New(params)
		if err != nil {
			print("Error creating Stripe customer: %v", err)
			return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: err.Error()})
		}
		err1 := InsertStripeCustomerId(config.DB, stripeCustomer.ID, userID)
		if err1 != nil {
			return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: err1.Error()})
		}
		checkStripeUser.StripeCustomerId = &stripeCustomer.ID
	}
	return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Stripe customer created", IsSuccess: true})
}

// AddCardToCustomer returns a SetupIntent for saving a card to customer Setup intent will send to Frontend to add customer card
// FE will use setup intent and return payment method id we will store pID to db and make transactions
// This function wont be use if pmid attach works
func GetClientSecret(c echo.Context) error {

	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Invalid user ID"})
	}

	user, err := getUserByID(config.DB, userID)
	if err != nil || user.StripeCustomerId == nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Stripe customer not found"})
	}

	stripe.Key = os.Getenv("STRIPE_SECRET")
	params := &stripe.SetupIntentParams{
		Customer: stripe.String(*user.StripeCustomerId),
		Usage:    stripe.String("off_session"),
	}

	si, err := setupintent.New(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: err.Error()})
	}

	_, err = GetUserConnectedAccount(config.DB, userID)

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			log.Printf("No connected account found for user %d when adding payment method", userID)
		} else {
			// Some other database error
			return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Database error: " + err.Error()})
		}
	}

	log.Printf("SetupIntent created with client_secret: %s", si.ClientSecret)
	response := resDto.ClientSecretResponse{
		ClientSecret: si.ClientSecret,
		APIResponse:  resDto.APIResponse{Message: "SetupIntent created", IsSuccess: true},
	}
	return c.JSON(http.StatusOK, response)
}

// CreateTopUpIntent creates a PaymentIntent for top-up || this func will call after card is added
// TODO: verify the toupup amount
func CreateTopUpIntent(c echo.Context) error {
	charge, err := GetChargeByType("card_topup")
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Error getting topup charge",
		})
	}

	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Invalid user ID"})
	}

	// Get request from context - receiving in higher currency
	var req reqDto.TopUpRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Invalid request format"})
	}

	if req.MethodToken == "" {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "method_token required"})
	}

	if req.Amount < 0.5 {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Minimum top up amount is 0.50"})
	}
	totalAmount := req.Amount + charge.Amount
	req.Amount = totalAmount
	user, err := getUserByID(config.DB, userID)
	if err != nil || user.StripeCustomerId == nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Stripe customer not found"})
	}
	//TODO: add condition with pm_id in where clause
	pmtMethodId, err := GetPaymentMethod(config.DB, userID, req.MethodToken)
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "No payment method found"})
	}

	stripe.Key = os.Getenv("STRIPE_SECRET")
	params := &stripe.PaymentIntentParams{
		Amount:             stripe.Int64(int64(req.Amount * 100)), // passed as lower currency - cents
		Currency:           stripe.String("eur"),
		Customer:           stripe.String(*user.StripeCustomerId),
		PaymentMethod:      stripe.String(pmtMethodId.MethodToken),
		PaymentMethodTypes: stripe.StringSlice([]string{"card"}),
		OffSession:         stripe.Bool(true),
		Confirm:            stripe.Bool(true),
		ConfirmationMethod: stripe.String("automatic"),
		Description:        stripe.String("Wallet Top-up"),
	}

	intent, err := paymentintent.New(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: err.Error()})
	}

	var sb strings.Builder
	sb.Write([]byte("Intent not succeeded. Intent status"))
	sb.Write([]byte(intent.Status))

	if intent.Status != stripe.PaymentIntentStatusSucceeded {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: sb.String()})
	}

	// add to user's wallet
	var topupRequest reqDto.TransferRequest
	topupRequest.FromUserID = userID
	topupRequest.ToUserID = userID
	topupRequest.WabisabiID = ""
	topupRequest.Amount = float64(intent.Amount) / 100 // intent is in cents so divide by 100 to get higher currency

	statusCode, err := InitiateTopUpTransactionDB(config.DB, topupRequest)
	if err != nil {
		return c.JSON(statusCode, resDto.APIResponse{Message: err.Error()})
	}

	print(intent.ClientSecret)
	topupEmailBody := constants.TopupBody(topupRequest.Amount, pmtMethodId.Brand+"-"+pmtMethodId.Last4)
	err1 := SendOTPEmail(user.Email, "Topup Wallet", topupEmailBody)
	if err1 != nil {

		fmt.Println("Error sending email:", err)
	}
	//return c.JSON(http.StatusOK, resDto.APIResponse{IsSuccess: true, Message: "Top up successful and added funds to wallet"})
	return GetHomePageDetails(c)
}

// FE will send the object to this endpoint
func CreateStripePaymentMethod(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Invalid user ID"})
	}

	var req models.StripePaymentMethod
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Stripe customer created", IsSuccess: true})
	}

	paymentMethod := models.StripePaymentMethod{
		UserID:           userID,
		StripeCustomerID: req.StripeCustomerID,
		MethodType:       req.MethodType,
		MethodToken:      req.MethodToken, //pm_id
		Brand:            req.Brand,
		Last4:            req.Last4,
		ExpMonth:         req.ExpMonth,
		ExpYear:          req.ExpYear,
		IsDefault:        req.IsDefault,
		CreatedAt:        time.Now(),
	}

	if err := CreateStripePaymentMethodDB(config.DB, &paymentMethod); err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to create payment method"})
	}

	return c.JSON(http.StatusOK, resDto.APIResponse{IsSuccess: true, Message: "Successfully create payment method"})
}

// Create connected account for payouts
func CreateConnectedAccount(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "error converting user id"})
	}

	_, err = CheckUserConnectedAccount(config.DB, userID)

	if errors.Is(err, gorm.ErrRecordNotFound) {
		var req reqDto.ConnectedAccountRequest
		if err := c.Bind(&req); err != nil {
			return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Invalid request format"})
		}

		CountryCode, err := GetISO2CodeByName(config.DB, req.Country)
		if err != nil {
			return err
		}

		stripe.Key = os.Getenv("STRIPE_SECRET")

		verificationSession, err := GetUserKYCVerificationSession(config.DB, userID)
		if err != nil {
			return err
		}

		vsParams := &stripe.IdentityVerificationSessionParams{}
		vsParams.AddExpand("last_verification_report")
		vs, err := verificationsession.Get(verificationSession.VerificationSessionID, vsParams)
		if err != nil {
			return err
		}

		frontFileID, err := reuploadStripeFile(vs.LastVerificationReport.Document.Files[0])
		if err != nil {
			return err
		}

		docParams := &stripe.PersonVerificationDocumentParams{}
		docParams.Front = stripe.String(frontFileID)

		if len(vs.LastVerificationReport.Document.Files) >= 2 {
			id, err := reuploadStripeFile(vs.LastVerificationReport.Document.Files[1])
			if err != nil {
				return err
			}
			docParams.Back = stripe.String(id)
		}

		params := &stripe.AccountParams{
			Type:         stripe.String("custom"),
			Country:      stripe.String(CountryCode),
			Email:        stripe.String(req.Email),
			BusinessType: stripe.String("individual"),
			ExternalAccount: &stripe.AccountExternalAccountParams{
				AccountNumber:     stripe.String(req.AccountNumber),
				Country:           stripe.String(CountryCode),
				Currency:          stripe.String(req.Currency),
				AccountHolderName: stripe.String(req.AccountHolderName),
				AccountHolderType: stripe.String(req.AccountHolderType),
			},
			Capabilities: &stripe.AccountCapabilitiesParams{
				Transfers: &stripe.AccountCapabilitiesTransfersParams{Requested: stripe.Bool(true)},
			},
			TOSAcceptance: &stripe.AccountTOSAcceptanceParams{
				Date: stripe.Int64(time.Now().Unix()),
				IP:   stripe.String(c.RealIP()),
			},
			Individual: &stripe.PersonParams{
				FirstName: stripe.String(req.FirstName),
				LastName:  stripe.String(req.LastName),
				Email:     stripe.String(req.Email),
				DOB: &stripe.DOBParams{
					Day:   stripe.Int64(int64(req.DOBDay)),
					Month: stripe.Int64(int64(req.DOBMonth)),
					Year:  stripe.Int64(int64(req.DOBYear)),
				},
				Address: &stripe.AccountAddressParams{
					Line1:      stripe.String(req.AddressLine1),
					City:       stripe.String(req.City),
					Country:    stripe.String(CountryCode),
					PostalCode: stripe.String("75001"),
				},
				Verification: &stripe.PersonVerificationParams{
					Document: docParams,
				},
			},
			BusinessProfile: &stripe.AccountBusinessProfileParams{
				ProductDescription: stripe.String(req.ProductDescription),
				MCC:                stripe.String("6012"),
				SupportEmail:       stripe.String(req.Email),
			},
		}
		params.AddMetadata("wspay_userId", strconv.FormatUint(uint64(userID), 10))

		acct, err := account.New(params)
		if err != nil {
			return err
		}

		record := models.StripeConnectedAccount{
			UserID:    userID,
			AccountID: acct.ID,
			KYCStatus: "pending",
		}

		if err := CreateStripeConnectedAccountRecord(config.DB, &record); err != nil {
			return err
		}

		return c.JSON(http.StatusOK, resDto.APIResponse{Message: "Account created"})
	} else if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Database error"})
	} else {
		return c.JSON(http.StatusConflict, resDto.APIResponse{Message: "Connected Account already exists"})
	}
}

// Transfer funds to a connected account (IBAN) WITHDRAWAL
func TransferToConnectedAccount(c echo.Context, accountID string, amount int64) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		message := "Error converting user id"
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: message, IsSuccess: false})
	}

	var req reqDto.WithdrawalRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Invalid request format", IsSuccess: false})
	}

	// Convert the amount to cents/smallest currency unit
	amountInSmallestUnit := int64(req.Amount * 100)
	if amountInSmallestUnit <= 0 {
		message := "Amount should be greater than zero."
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: message, IsSuccess: false})
	}

	// Start a transaction
	tx := config.DB.Begin()

	senderWallet, err := GetWalletAmountSender(config.DB, userID)
	if err != nil {
		tx.Rollback()
		message := "Wallet not found"
		return c.JSON(http.StatusNotFound, resDto.APIResponse{Message: message, IsSuccess: false})
	}

	senderAmountStr, err := DecryptAES(senderWallet.Amount)
	if err != nil {
		tx.Rollback()
		message := "Error decrypting wallet balance"
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: message, IsSuccess: false})
	}

	senderAmount, err := strconv.ParseFloat(senderAmountStr, 64)
	if err != nil {
		tx.Rollback()
		message := "Error parsing wallet balance"
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: message, IsSuccess: false})
	}

	if senderAmount < req.Amount {
		tx.Rollback()
		message := "Insufficient balance in wallet"
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: message, IsSuccess: false})
	}

	var connectedAccount models.StripeConnectedAccount
	err = tx.Where("user_id = ?", userID).First(&connectedAccount).Error
	if err != nil {
		tx.Rollback()
		return c.JSON(http.StatusNotFound, resDto.APIResponse{Message: "Connected account not found", IsSuccess: false})
	}

	stripe.Key = os.Getenv("STRIPE_SECRET")

	// Prepare the transfer parameters
	transferParams := &stripe.TransferParams{
		Amount:      stripe.Int64(amountInSmallestUnit),
		Currency:    stripe.String("eur"),
		Destination: stripe.String(connectedAccount.AccountID),
	}

	transferResult, err := transfer.New(transferParams)
	if err != nil {
		tx.Rollback()
		message := "Error initiating the transfer: " + err.Error()
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: message, IsSuccess: false})
	}

	senderAmount -= req.Amount

	encryptedSenderAmount, err := EncryptAES(fmt.Sprintf("%.4f", senderAmount))
	if err != nil {
		tx.Rollback()
		message := "Failed to encrypt wallet balance"
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: message, IsSuccess: false})
	}

	senderWallet.Amount = encryptedSenderAmount
	if err := tx.Save(&senderWallet).Error; err != nil {
		tx.Rollback()
		message := "Failed to update wallet balance"
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: message, IsSuccess: false})
	}

	transaction := models.Transaction{
		UserID:   userID,
		ToFrom:   connectedAccount.AccountID,
		Amount:   req.Amount,
		WalletID: senderWallet.ID,
		DrCr:     "Debit",
		TxnType:  "withdrawal",
		Status:   "COMPLETED",
	}

	err = tx.Create(&transaction).Error
	if err != nil {
		tx.Rollback()
		message := "Failed to record transaction"
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: message, IsSuccess: false})
	}

	err = tx.Commit().Error
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Transaction failed", IsSuccess: false})
	}

	log.Printf("Transfer successful with ID: %s", transferResult.ID)

	return c.JSON(http.StatusOK, resDto.APIResponse{Message: "Transfer successful", IsSuccess: true})
}

func GetUserSavedCards(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "error converting user id"})
	}

	cards, err := fetchSavedCards(userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "error fetching cards"})
	}

	return c.JSON(http.StatusOK, resDto.UserCardResponseDTO{
		APIResponse: resDto.APIResponse{IsSuccess: true, Message: "successfully fetched cards"},
		Cards:       cards,
	})
}

// TODO duplicate pmId validation
func AddCardToCustomer(c echo.Context) error {
	// Get the user's ID
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		message := "Error converting user id"
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: message})
	}
	// Create a Stripe customer if not exists
	checkStripeUser, err := CheckStripeCustomer(config.DB, userID)
	if err != nil {
		message := "Error stripe customer from database"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: message, IsSuccess: false})
	}
	if checkStripeUser.StripeCustomerId == nil {
		params := &stripe.CustomerParams{
			Email: stripe.String(checkStripeUser.Email),
			Name:  stripe.String(checkStripeUser.FirstName + " " + checkStripeUser.LastName),
		}
		params.AddMetadata("wspay_userId", strconv.FormatUint(uint64(userID), 10))
		// Create the customer
		stripeCustomer, err := customer.New(params)
		if err != nil {
			print("Error creating Stripe customer: %v", err)
			return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: err.Error()})
		}
		err1 := InsertStripeCustomerId(config.DB, stripeCustomer.ID, userID)
		if err1 != nil {
			return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: err1.Error()})
		}
		checkStripeUser.StripeCustomerId = &stripeCustomer.ID
	}
	var req models.StripePaymentMethod
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "req binding error", IsSuccess: false})

	}

	// attach payment method to stripe customer
	params := &stripe.PaymentMethodAttachParams{
		Customer: checkStripeUser.StripeCustomerId,
	}
	_, err1 := paymentmethod.Attach(
		req.MethodToken, // pm_xxx
		params,
	)
	if err1 != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Error attaching payment method"})
	}

	// Retrieve the payment method
	pm, err := paymentmethod.Get(req.MethodToken, nil)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Error retrieving payment method"})
	}
	// Check if it's a card type
	if pm.Type != stripe.PaymentMethodTypeCard || pm.Card == nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "payment method is not a card"})
	}

	// Check if this is the user's first saved card
	var existingCardsCount int64
	if err := config.DB.Model(&models.StripePaymentMethod{}).Where("user_id = ?", userID).Count(&existingCardsCount).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to check existing cards"})
	}

	isDefault := req.IsDefault
	if existingCardsCount == 0 {
		isDefault = true // make first card default
	}
	// TODO change grouped transactions to include those too

	var cardIcon models.CardIcons
	config.DB.Where("brand_name = ?", string(pm.Card.Brand)).First(&cardIcon)

	paymentMethod := models.StripePaymentMethod{
		UserID:           userID,
		StripeCustomerID: *checkStripeUser.StripeCustomerId,
		MethodType:       string(pm.Type),
		MethodToken:      req.MethodToken, //pm_id
		Brand:            string(pm.Card.Brand),
		Last4:            pm.Card.Last4,
		ExpMonth:         int(pm.Card.ExpMonth),
		ExpYear:          int(pm.Card.ExpYear),
		IsDefault:        isDefault,
		CreatedAt:        time.Now(),
		CardIcon:         cardIcon.Icon,
		CardUserName:     req.CardUserName,
	}

	if err := config.DB.Create(&paymentMethod).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to add card"})

	}
	err2 := SendOTPEmail(checkStripeUser.Email, "Card Added", "You've successfully attach a card")
	if err2 != nil {

		fmt.Println("Error sending email:", err)
	}
	return GetUserSavedCards(c)
	//return c.JSON(http.StatusOK, resDto.APIResponse{IsSuccess: true, Message: "Successfully add card"})

}

// SetDefaultCard sets a specific card as the default payment method
func SetDefaultCard(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "error converting user id"})
	}

	// Get the payment method ID from request
	var req struct {
		PaymentMethodID string `json:"payment_method_id"`
	}
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Invalid request format"})
	}

	// Start a transaction
	tx := config.DB.Begin()

	// First, set all cards to non-default
	if err := tx.Model(&models.StripePaymentMethod{}).
		Where("user_id = ?", userID).
		Update("is_default", false).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to update cards"})
	}

	// Then, set the selected card as default
	if err := tx.Model(&models.StripePaymentMethod{}).
		Where("user_id = ? AND method_token = ?", userID, req.PaymentMethodID).
		Update("is_default", true).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to set default card"})
	}

	// Get the user's Stripe customer ID
	var user models.User
	if err := tx.Where("id = ?", userID).First(&user).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "User not found"})
	}

	if user.StripeCustomerId == nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Stripe customer not found"})
	}

	// Update the default payment method in Stripe
	stripe.Key = os.Getenv("STRIPE_SECRET")
	params := &stripe.CustomerParams{
		InvoiceSettings: &stripe.CustomerInvoiceSettingsParams{
			DefaultPaymentMethod: stripe.String(req.PaymentMethodID),
		},
	}
	_, err = customer.Update(*user.StripeCustomerId, params)
	if err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to update Stripe customer"})
	}

	if err := tx.Commit().Error; err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Transaction failed"})
	}

	// return c.JSON(http.StatusOK, resDto.APIResponse{
	// 	IsSuccess: true,
	// 	Message:   "Default card updated successfully",
	// })
	cards, err := fetchSavedCards(userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "error fetching cards"})
	}

	return c.JSON(http.StatusOK, resDto.UserCardResponseDTO{
		APIResponse: resDto.APIResponse{IsSuccess: true, Message: "Default card updated successfully"},
		Cards:       cards,
	})
}

// DeleteCard removes a card from both the database and Stripe
func DeleteCard(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "error converting user id"})
	}

	// Get the payment method ID from request
	var req struct {
		PaymentMethodID string `json:"payment_method_id"`
	}
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Invalid request format"})
	}

	// Start a transaction
	tx := config.DB.Begin()

	// Get the card details before deletion
	var card models.StripePaymentMethod
	if err := tx.Where("user_id = ? AND method_token = ?", userID, req.PaymentMethodID).First(&card).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusNotFound, resDto.APIResponse{Message: "Card not found"})
	}

	// Delete the card from Stripe
	_, err = paymentmethod.Detach(req.PaymentMethodID, nil)
	if err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to delete card from Stripe"})
	}

	// Delete the card from database
	if err := tx.Where("user_id = ? AND method_token = ?", userID, req.PaymentMethodID).
		Delete(&models.StripePaymentMethod{}).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to delete card from database"})
	}

	if err := tx.Commit().Error; err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Transaction failed"})
	}

	// If the deleted card was default, set another card as default
	if card.IsDefault {
		// Get the most recently added card
		var newDefaultCard models.StripePaymentMethod
		if err := tx.Where("user_id = ?", userID).
			Order("created_at desc").
			First(&newDefaultCard).Error; err == nil {
			// Set the new default card
			if err := tx.Model(&models.StripePaymentMethod{}).
				Where("user_id = ? AND method_token = ?", userID, newDefaultCard.MethodToken).
				Update("is_default", true).Error; err != nil {
				tx.Rollback()
				return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to set new default card"})
			}

			// Update Stripe customer's default payment method
			user, err := getUserByID(tx, userID)
			if err != nil {
				tx.Rollback()
				return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to get user details"})
			}

			if user.StripeCustomerId != nil {
				stripe.Key = os.Getenv("STRIPE_SECRET")
				params := &stripe.CustomerParams{
					InvoiceSettings: &stripe.CustomerInvoiceSettingsParams{
						DefaultPaymentMethod: stripe.String(newDefaultCard.MethodToken),
					},
				}
				_, err = customer.Update(*user.StripeCustomerId, params)
				if err != nil {
					tx.Rollback()
					return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to update Stripe customer"})
				}
			}
		}
	}

	if err := tx.Commit().Error; err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "DB Transaction failed"})
	}

	// Fetch updated saved cards
	cards, err := fetchSavedCards(userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "error fetching cards"})
	}

	return c.JSON(http.StatusOK, resDto.UserCardResponseDTO{
		APIResponse: resDto.APIResponse{IsSuccess: true, Message: "Card deleted successfully"},
		Cards:       cards,
	})
}

// Helper function to update Stripe customer's default payment method
func updateStripeDefaultPaymentMethod(userID uint, paymentMethodID string) error {
	var user models.User
	if err := config.DB.Where("id = ?", userID).First(&user).Error; err != nil {
		return err
	}

	if user.StripeCustomerId == nil {
		return fmt.Errorf("stripe customer not found")
	}

	stripe.Key = os.Getenv("STRIPE_SECRET")
	params := &stripe.CustomerParams{
		InvoiceSettings: &stripe.CustomerInvoiceSettingsParams{
			DefaultPaymentMethod: stripe.String(paymentMethodID),
		},
	}
	_, err := customer.Update(*user.StripeCustomerId, params)
	return err
}
