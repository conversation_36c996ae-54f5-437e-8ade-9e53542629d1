package models

import "gorm.io/gorm"

type Conversation struct {
	gorm.Model

	IsGroup   bool     `gorm:"not null" json:"is_group"`
	Name      string  `gorm:"size:255" json:"name"`
	CreatedBy uint     `gorm:"not null" json:"created_by"`
	ProjectID *uint    `gorm:"column:project_id" json:"project_id"`

	Creator User     `gorm:"foreignKey:CreatedBy;references:Id" json:"creator"`
	Project *Projects `gorm:"foreignKey:ProjectID;references:ID" json:"project"`
}
