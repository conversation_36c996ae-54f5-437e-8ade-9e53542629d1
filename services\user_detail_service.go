package services

import (
	"auth-service/config"
	resDTO "auth-service/dtos/Response"
	"auth-service/models"
	"fmt"
	"net/http"
	"os"

	"github.com/labstack/echo/v4"
)

func GetUserDetails(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.UserDetailsResponseDTO{
			APIResponse: resDTO.APIResponse{
				IsSuccess: false,
				Message:   "Error converting user id",
			},
		})
	}

	userDetails, err := getUserDetailsByUserID(config.DB, userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.UserDetailsResponseDTO{
			APIResponse: resDTO.APIResponse{
				IsSuccess: false,
				Message:   err.Error(),
			},
		})
	}

	response := resDTO.UserDetailsResponseDTO{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "Fetched User Details Successfully",
		},
		User: *userDetails, // DTO
	}

	return c.JSON(http.StatusOK, response)
}

// func InsertUserDetails(c echo.Context) error {
// 	message := "User details updated successfully"
// 	userID, err := InterfaceToUint(c.Get("user_id"))
// 	if err != nil {
// 		return c.JSON(http.StatusInternalServerError, resDTO.UserDetailsResponseDTO{APIResponse: resDTO.APIResponse{Message:"Error converting user id"}})
// 	}

// 	var updates reqDTO.UserDetailsUpdateDTO
// 	if err := c.Bind(&updates); err != nil {
// 		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Invalid request"})
// 	}

// 	var existing models.UserDetails
// 	if err := config.DB.Where("user_id = ?", userID).First(&existing).Error; err != nil {
// 		return c.JSON(http.StatusNotFound, resDTO.APIResponse{Message: "User details not found"})
// 	}

// 	if updates.NicFrontBase64 != "" {
// 		frontImageURL, err := uploadBase64ToS3(updates.NicFrontBase64, os.Getenv("AWS_S3_BUCKET_NAME"), fmt.Sprintf("user-%d-nic-card-front.png", userID))
// 		if err != nil {
// 			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to upload front image"})
// 		}
// 		existing.NicURLFront = frontImageURL
// 	}

// 	if updates.NicBackBase64 != "" {
// 		backImageURL, err := uploadBase64ToS3(updates.NicBackBase64, os.Getenv("AWS_S3_BUCKET_NAME"), fmt.Sprintf("user-%d-nic-card-back.png", userID))
// 		if err != nil {
// 			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to upload back image"})
// 		}
// 		existing.NicURLBack = backImageURL
// 	}

// 	if updates.UserPhotoBase64 != "" {
// 		userPhotoURL, err := uploadBase64ToS3(updates.UserPhotoBase64, os.Getenv("AWS_S3_BUCKET_NAME"), fmt.Sprintf("user-%d-personal-photo.png", userID))
// 		if err != nil {
// 			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to upload user image"})
// 		}
// 		existing.UserPhotoURL = userPhotoURL
// 	}

// 	if updates.Email != "" {
// 		existing.Email = updates.Email
// 	}
// 	if updates.DateOfBirth != "" {
// 		existing.DateOfBirth = updates.DateOfBirth
// 	}
// 	if updates.Nationality != "" {
// 		existing.Nationality = updates.Nationality
// 	}
// 	if updates.PlaceOfBirth != "" {
// 		existing.PlaceOfBirth = updates.PlaceOfBirth
// 	}
// 	if updates.NIF != "" {
// 		existing.NIF = updates.NIF
// 	}
// 	if updates.Street != "" {
// 		existing.Street = updates.Street
// 	}
// 	if updates.Province != "" {
// 		existing.Province = updates.Province
// 	}
// 	if updates.PostalCode != "" {
// 		existing.PostalCode = updates.PostalCode
// 	}
// 	if updates.City != "" {
// 		existing.City = updates.City
// 	}
// 	if updates.Country != "" {
// 		existing.Country = updates.Country
// 	}
// 	if updates.Occupation != "" {
// 		existing.Occupation = updates.Occupation
// 	}
// 	if updates.Company != "" {
// 		existing.Company = updates.Company
// 	}
// 	if updates.EstimatedIncome != "" {
// 		existing.EstimatedIncome = updates.EstimatedIncome
// 	}
// 	if updates.SourceOfFunds != "" {
// 		existing.SourceOfFunds = updates.SourceOfFunds
// 	}
// 	if updates.PageIndex != 0 {
// 		existing.PageIndex = updates.PageIndex + 1
// 	}
// 	existing.IsVerify = updates.IsVerify
// 	existing.IsPep = updates.IsPep
// 	existing.IsTaxResidentElsewhere = updates.IsTaxResidentElsewhere

// 	if err := config.DB.Save(&existing).Error; err != nil {
// 		msg := "Update failed"
// 		if IsDuplicateKeyError(err) {
// 			msg = "Already exists: " + err.Error()
// 		}
// 		c.Set("customMessage", msg)
// 		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: msg})
// 	}

//		c.Set("customMessage", message)
//		return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
//	}
func InsertUserDetails(c echo.Context) error {
	message := "User details updated successfully"
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.UserDetailsResponseDTO{APIResponse: resDTO.APIResponse{Message: "Error converting user id"}})
	}

	// Get existing user details
	var existing models.UserDetails
	if err := config.DB.Where("user_id = ?", userID).First(&existing).Error; err != nil {
		return c.JSON(http.StatusNotFound, resDTO.APIResponse{Message: "User details not found"})
	}

	// Create a map to store updates
	updates := make(map[string]interface{})

	// Parse request body into a map
	var requestMap map[string]interface{}
	if err := c.Bind(&requestMap); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Invalid request"})
	}

	// Handle base64 image uploads first
	if nicFront, ok := requestMap["nic_front_base64"].(string); ok && nicFront != "" {
		frontImageURL, err := uploadBase64ToS3(nicFront, os.Getenv("AWS_S3_BUCKET_NAME"), fmt.Sprintf("user-%d-nic-card-front.png", userID))
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to upload front image"})
		}
		updates["nic_url_front"] = frontImageURL
	}

	if nicBack, ok := requestMap["nic_back_base64"].(string); ok && nicBack != "" {
		backImageURL, err := uploadBase64ToS3(nicBack, os.Getenv("AWS_S3_BUCKET_NAME"), fmt.Sprintf("user-%d-nic-card-back.png", userID))
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to upload back image"})
		}
		updates["nic_url_back"] = backImageURL
	}

	if userPhoto, ok := requestMap["user_photo_base64"].(string); ok && userPhoto != "" {
		userPhotoURL, err := uploadBase64ToS3(userPhoto, os.Getenv("AWS_S3_BUCKET_NAME"), fmt.Sprintf("user-%d-personal-photo.png", userID))
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to upload user image"})
		}
		updates["user_photo_url"] = userPhotoURL
	}

	// Handle other fields
	fieldMappings := map[string]string{
		"email":            "email",
		"date_of_birth":    "date_of_birth",
		"nationality":      "nationality",
		"place_of_birth":   "place_of_birth",
		"nif":              "nif",
		"street":           "street",
		"province":         "province",
		"postal_code":      "postal_code",
		"city":             "city",
		"country":          "country",
		"occupation":       "occupation",
		"company":          "company",
		"estimated_income": "estimated_income",
		"source_of_funds":  "source_of_funds",
	}

	for jsonField, dbField := range fieldMappings {
		if value, exists := requestMap[jsonField]; exists && value != nil {
			updates[dbField] = value
		}
	}

	// Special handling for page_index
	if pageIndex, exists := requestMap["page_index"]; exists {
		if pageIndex != nil {
			// Convert to float64 first (JSON numbers are decoded as float64)
			if pageIndexFloat, ok := pageIndex.(float64); ok {
				updates["page_index"] = int(pageIndexFloat) + 1
			}
		}
	}

	// Special handling for boolean fields
	if isVerify, exists := requestMap["is_verify"]; exists {
		if isVerify != nil {
			updates["is_verify"] = isVerify
		}
	}

	if isPep, exists := requestMap["is_pep"]; exists {
		if isPep != nil {
			updates["is_pep"] = isPep
		}
	}

	if isTaxResident, exists := requestMap["is_tax_resident_elsewhere"]; exists {
		if isTaxResident != nil {
			updates["is_tax_resident_elsewhere"] = isTaxResident
		}
	}

	// If no updates provided
	if len(updates) == 0 {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "No updates provided"})
	}

	// Update the user details
	if err := config.DB.Model(&existing).Updates(updates).Error; err != nil {
		msg := "Update failed"
		if IsDuplicateKeyError(err) {
			msg = "Already exists: " + err.Error()
		}
		c.Set("customMessage", msg)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: msg})
	}

	c.Set("customMessage", message)
	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
}
