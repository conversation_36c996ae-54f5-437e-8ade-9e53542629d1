package models

import (
	"gorm.io/gorm"
)

type StripeConnectedAccount struct {
	gorm.Model
	UserID          uint   `gorm:"not null;uniqueIndex"`               // One-to-one with User
	AccountID       string `gorm:"not null;unique;size:255"`                    // Stripe connected account ID
	KYCStatus       string `gorm:"size:20;default:'pending'"` // pending | verified | failed
	Capabilities    string `gorm:"type:text;size:255"`                          // Optional: JSON or comma-separated list (e.g. "transfers,card_payments")
	PaymentMethodID string `gorm:"default:null;size:255"`                       // Optional - populated after card setup
	User            User   `gorm:"foreignKey:UserID;references:id"`
}
