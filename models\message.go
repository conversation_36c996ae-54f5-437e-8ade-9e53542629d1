package models

import (
	"gorm.io/gorm"
)

type Message struct {
	gorm.Model

	ConversationID uint    `gorm:"not null" json:"conversation_id"`
	SenderID       uint    `gorm:"not null" json:"sender_id"`
	Content        *string `gorm:"type:text" json:"content"`
	MessageType    string  `gorm:"size:20;default:'text'" json:"message_type"`

	Conversation Conversation `gorm:"foreignKey:ConversationID;references:ID;constraint:OnDelete:CASCADE" json:"conversation"`
	Sender       User         `gorm:"foreignKey:SenderID;references:Id" json:"sender"`
}
