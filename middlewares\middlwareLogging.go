package middlewares

import (
	"auth-service/config"

	"github.com/labstack/echo/v4"
)

func LoggingMiddleware(next echo.HandlerFunc) echo.HandlerFunc {
	apiLogger := config.ConfigureBaseLogger()

	return func(c echo.Context) error {

		err := next(c)

		// Handling nil values here to avoid panic during type asserting nil values

		customMessage := "No message"
		msg := c.Get("customMessage")
		if msg != nil {
			customMessage = msg.(string)
		}

		service := "unknown_service"
		svc := c.Get("serviceName")
		if svc != nil {
			service = svc.(string)
		}
		if service != "unknown_service" && customMessage != "No message" {
			apiLogger.Info().
				Str("Message", customMessage).
				Str("Service", service).
				Send()
		}
		return err
	}
}
