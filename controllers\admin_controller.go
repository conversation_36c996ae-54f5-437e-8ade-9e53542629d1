package controllers

import (
	"auth-service/services"

	"github.com/labstack/echo/v4"
)

func AdminLogin(c echo.Context) error {
	return services.AdminLogin(c)
}

func CreateAdmin(c echo.Context) error {
	return services.CreateAdmin(c)
}

// Add this to your existing admin_controller.go file

func GetPendingKYCVerifications(c echo.Context) error {
	return services.GetPendingKYCVerifications(c)
}

// Add this to your existing admin_controller.go file

func GetKYCVerificationDetails(c echo.Context) error {
	return services.GetKYCVerificationDetails(c)
}

// Add this to your existing admin_controller.go file

func UpdateAdminDetails(c echo.Context) error {
	return services.UpdateAdminDetails(c)
}

func KycAction(c echo.Context) error {
	return services.HandleKYCVerificationAction(c)
}
