package ResponseDTO

import "time"

// TOOD extract DTOs not being used, extract common DTOs and try to use a single DTO everywhere 

type RawTransactionGroup struct {
	Date         time.Time `json:"date"`
	Transactions string `json:"transactions"`
}

//TODO: here now we use recent transactions as a base for transaction history
type TransactionHistory struct {
	Name      string    `json:"name"`
	BankName  string    `json:"bank_name"`
	WalletID  string    `json:"wallet_id"`
	DrCr      string    `json:"dr_cr"`
	Amount    float64   `json:"amount"`
	CreatedAt time.Time `json:"txn_date"`
}

// New struct to represent a group of transactions for a specific date
type DateTransactionGroup struct {
	Date        int64               `json:"date"`
	TransDetail []RecentTransactionsNew `json:"trans_detail"`
}

type TransactionHistoryResponseDTO struct {
	APIResponse
	TransactionHistory []DateTransactionGroup `json:"transaction_history"`
}

type RecentTransactions struct {
	UserID   uint      `gorm:"not null;constraint:OnDelete:CASCADE;foreignKey:UserID;references:ID" json:"user_id"`
	WalletID string    `gorm:"size:100;not null;constraint:OnDelete:CASCADE;foreignKey:WalletID;references:ID" json:"wallet_id"`
	DrCr     string    `gorm:"size:10;not null" json:"dr_cr"` // "DR" for debit, "CR" for credit
	TxnDate  int64	   `gorm:"autoCreateTime" json:"txn_date"`
	TxnType  string    `gorm:"size:50;not null" json:"txn_type"`
	ToFrom   string    `gorm:"size:100;not null" json:"to_from"`
	Amount   float64   `gorm:"not null" json:"amount"`
	Status   string    `gorm:"size:20;not null" json:"status"`
	Purpose  string    `gorm:"size:50" json:"purpose"`
	Icon     string    `json:"icon"`
	CardIcon string	   `json:"card_icon"`
	//WsId         string    `json:"ws_id"`
	//FullName     string    `json:"full_name"`
	SenderWsID   string `json:"sender_ws_id"`
	SenderName   string `json:"sender_name"`
	ReceiverWsID string `json:"receiver_ws_id"`
	ReceiverName string `json:"receiver_name"`
	Brand        string `json:"brand"`
	Last4        string `json:"last4"`
}

type RecentTransactionsNew struct {
	UserID       uint    `json:"user_id"`
	WalletID     string  `json:"wallet_id"`
	DrCr         string  `json:"dr_cr"`
	TxnDate      int64   `json:"txn_date"`      // Unix timestamp
	TxnType      string  `json:"txn_type"`
	ToFrom       string  `json:"to_from"`
	Amount       float64 `json:"amount"`
	Status       string  `json:"status"`
	CreatedAt    int64   `json:"created_at"`    // Unix timestamp
	UpdatedAt    int64   `json:"updated_at"`    // Unix timestamp
	DeletedAt    *int64  `json:"deleted_at"`    // Unix timestamp or null
	Purpose      string  `json:"purpose"`
	Icon         string  `json:"icon"`
	CardIcon     string  `json:"card_icon"`
	SenderWsID   string  `json:"sender_ws_id"`
	SenderName   string  `json:"sender_name"`
	ReceiverWsID string  `json:"receiver_ws_id"`
	ReceiverName string  `json:"receiver_name"`
	Brand        string  `json:"brand"`
	Last4        string  `json:"last4"`
}

type GraphTransactionNew struct {
	UserID       uint    `json:"user_id"`
	WalletID     string  `json:"wallet_id"`
	DrCr         string  `json:"dr_cr"`
	TxnDate      string  `json:"txn_date"`      // String timestamp
	TxnType      string  `json:"txn_type"`
	ToFrom       string  `json:"to_from"`
	Amount       float64 `json:"amount"`
	Status       string  `json:"status"`
	CreatedAt    string  `json:"created_at"`    // String timestamp
	UpdatedAt    string  `json:"updated_at"`    // String timestamp
	DeletedAt    *string `json:"deleted_at"`    // String timestamp or null
	Purpose      string  `json:"purpose"`
	Icon         string  `json:"icon"`
	CardIcon     string  `json:"card_icon"`
	SenderWsID   string  `json:"sender_ws_id"`
	SenderName   string  `json:"sender_name"`
	ReceiverWsID string  `json:"receiver_ws_id"`
	ReceiverName string  `json:"receiver_name"`
	Brand        string  `json:"brand"`
	Last4        string  `json:"last4"`
}

type AggregatedGraphDataUnix struct {
	Key    int64   `json:"key"`    // Unix timestamp
	Amount float64 `json:"amount"` // Total amount for the day
}

// New DTO for graph transactions with Unix timestamps
type GraphTransactionUnix struct {
	UserID       uint    `json:"user_id"`
	WalletID     string  `json:"wallet_id"`
	DrCr         string  `json:"dr_cr"`
	TxnDate      int64   `json:"txn_date"`      // Unix timestamp
	TxnType      string  `json:"txn_type"`
	ToFrom       string  `json:"to_from"`
	Amount       float64 `json:"amount"`
	Status       string  `json:"status"`
	CreatedAt    int64   `json:"created_at"`    // Unix timestamp
	UpdatedAt    int64   `json:"updated_at"`    // Unix timestamp
	DeletedAt    *int64  `json:"deleted_at"`    // Unix timestamp or null
	Purpose      string  `json:"purpose"`
	Icon         string  `json:"icon"`
	CardIcon     string  `json:"card_icon"`
	SenderWsID   string  `json:"sender_ws_id"`
	SenderName   string  `json:"sender_name"`
	ReceiverWsID string  `json:"receiver_ws_id"`
	ReceiverName string  `json:"receiver_name"`
	Brand        string  `json:"brand"`
	Last4        string  `json:"last4"`
}

// New response DTO for graph API with Unix timestamps
type GraphTransaction7DaysUnixResponseDTO struct {
	APIResponse
	GraphData          []AggregatedGraphDataUnix `json:"graph_data"`
	TransactionHistory []GraphTransactionUnix    `json:"transaction_history"`
}