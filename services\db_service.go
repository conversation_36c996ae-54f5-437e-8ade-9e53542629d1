package services

import (
	"auth-service/config"
	reqDTO "auth-service/dtos/Request"
	resDTO "auth-service/dtos/Response"
	"strings"

	//resDto "auth-service/dtos/Response"
	"auth-service/models"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"github.com/markbates/goth"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// GetPaymentMethod returns the default payment method ID for a user
func GetPaymentMethod(db *gorm.DB, userID uint, methodToken string) (models.StripePaymentMethod, error) {
	var paymentMethod models.StripePaymentMethod
	if err := db.Where("user_id = ?", userID).Where("method_token = ?", methodToken).First(&paymentMethod).Error; err != nil {
		return paymentMethod, err
	}

	if paymentMethod.MethodToken == "" {
		return paymentMethod, fmt.Errorf("no payment method found for user")
	}

	return paymentMethod, nil
}

func GetUserKYCInfo(db *gorm.DB, userID uint) (*resDTO.UserKYCInfo, error) {
	var result resDTO.UserKYCInfo

	err := db.Table("user_details AS ud").
		Select("ud.is_verify, kv.status", "sp.method_token").
		Joins("INNER JOIN kyc_verifications kv ON ud.user_id = kv.user_id").
		Joins("LEFT JOIN stripe_payment_methods sp ON ud.user_id = sp.user_id").
		Where("ud.user_id = ?", userID).Order("sp.is_default DESC").
		Limit(1).
		Scan(&result).Error

	if err != nil {
		return nil, err
	}

	return &result, nil
}
func getUserByEmail(db *gorm.DB, email string) (*models.User, error) {
	var user models.User
	if err := db.Where("email = ?", email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return &user, nil // No record found, return nil instead of error
		}
		return nil, err // Return actual database error
	}

	return &user, nil
}

func getUserByWSID(db *gorm.DB, wsID string) (*models.User, error) {
	var user models.User
	if err := db.Where("ws_id = ?", wsID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return &user, nil // No record found, return nil instead of error
		}
		return nil, err // Return actual database error
	}
	return &user, nil
}

func GetUserByPhoneNumber(db *gorm.DB, PhoneNumber string) (*models.User, error) {
	var user models.User
	if err := db.Where("phone_number = ?", PhoneNumber).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return &user, nil // No record found, return nil instead of error
		}
		return nil, err // Return actual database error
	}
	return &user, nil
}

func createUser(db *gorm.DB, user *models.User) (*resDTO.UserResponseDTO, error) {
	err := db.Transaction(func(tx *gorm.DB) error {
		// Insert user
		if user.Password != "" {
			if err := tx.Save(&user).Error; err != nil {
				return err
			}
		} else {
			if err := tx.Select("email", "first_name", "last_name").Create(&user).Error; err != nil {
				return err
			}
		}

		// Insert user details
		userDetails := models.UserDetails{
			UserID: user.Id,
			Email:  user.Email,
		}
		if err := tx.Select("UserID", "Email").Create(&userDetails).Error; err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}
	userResponse := &resDTO.UserResponseDTO{
		Id:          user.Id,
		Email:       user.Email,
		FirstName:   user.FirstName,
		LastName:    user.LastName,
		PhoneNumber: user.PhoneNumber,
		CreatedAt:   user.CreatedAt,
	}
	return userResponse, nil
}

func UpdateUserContact(db *gorm.DB, user *models.User, email string) error {
	if err := db.Model(&user).Where("email = ?", email).Update("phone_number", user.PhoneNumber).Error; err != nil {
		return err
	}
	return nil
}

func UpdateUserCredentials(db *gorm.DB, user *models.User, pin, email, wsid string) error {
	if err := db.Model(&user).Where("email = ?", email).Updates(map[string]interface{}{
		"pin":   pin,
		"ws_id": wsid,
	}).Error; err != nil {
		return err
	}
	return nil
}

func UpdatePinCode(db *gorm.DB, user *models.User, hashedPin string, userID uint) error {
	if err := db.Model(&user).Where("id = ?", userID).Update("pin", hashedPin).Error; err != nil {
		return err
	}
	return nil
}

func ChangePin(db *gorm.DB, user *models.User, hashedPin string, userID uint) error {
	if err := db.Model(&user).Where("id = ?", userID).Update("pin", hashedPin).Error; err != nil {
		return err
	}
	return nil
}

func UpdatePassword(db *gorm.DB, user *models.User, email, hashedPassword string) {
	db.Model(&models.User{}).Where("email = ?", email).Update("password", hashedPassword)
}

// UpdateUserDetails updates specific fields in the user_details table
func UpdateUserDetails(userID interface{}, updates *reqDTO.UserDetailsUpdateDTO) error {
	// Perform the update
	err := config.DB.Model(&models.UserDetails{}).Where("user_id = ?", userID).Updates(updates).Error
	if err != nil {
		return err // Return error if update fails
	}
	return nil
}

func InsertBankDetails(req *models.BankDetails) error {

	err := config.DB.Model(&models.BankDetails{}).Create(req).Error
	if err != nil {
		return err
	}
	return nil
}

func GetQuestionsFromDatabase(db *gorm.DB) (*resDTO.QuestionResponse, error) {
	// 	var questions []models.Question
	// 	if err := db.Find(&questions).Error; err != nil {
	// 		return nil, err
	// 	}
	// 	return &questions, nil
	// }
	var questions []models.Question
	var occupations []models.Occupation
	var incomes []models.EstimatedAnnualIncome

	// Fetch all data
	if err := db.Find(&questions).Error; err != nil {
		return nil, err
	}
	if err := db.Find(&occupations).Error; err != nil {
		return nil, err
	}
	if err := db.Find(&incomes).Error; err != nil {
		return nil, err
	}

	// Build response
	return &resDTO.QuestionResponse{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "Fetched setup data successfully",
		},
		Question:             &questions,
		Occupation:           &occupations,
		EstimatedAnnualIcome: &incomes,
	}, nil
}

func InsertQuestionsAttempt(db *gorm.DB, req []models.QuestionsAttempt) error {
	err := db.Model(&models.QuestionsAttempt{}).Create(&req).Error
	if err != nil {
		return err
	}
	return nil
}

func InsertOrUpdateQuestionsAttempt(db *gorm.DB, req []models.QuestionsAttempt) error {
	err := db.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}, {Name: "question_id"}},           // Composite key
		DoUpdates: clause.AssignmentColumns([]string{"selected_option", "created_at"}), // Fields to update
	}).Create(&req).Error

	if err != nil {
		return err
	}
	return nil
}

func SearchBeneficiaries(userID uint, searchTerm string, isFav bool) ([]resDTO.BeneficiaryWalletMapDTO, error) {
	results := make([]resDTO.BeneficiaryWalletMapDTO, 0)

	pattern := "%" + searchTerm + "%"

	var err error
	query := config.DB.Table("beneficiaries b").
		Select(`b.beneficiary_id, 
                b.name as beneficiary_name,
                u.ws_id, 
                "" AS wallet_id,
				b.beneficiary_type_id,
				bt.name as beneficiary_type,
                b.created_at,
				b.is_fav`).
		Joins("JOIN users u ON u.id = b.beneficiary_id").
		Joins("JOIN beneficiary_types bt ON bt.id = b.beneficiary_type_id")

	if isFav {
		err = query.Where("b.user_id = ? AND (b.name LIKE ? OR users.ws_id LIKE ?) AND b.is_fav = ?", userID, pattern, pattern, 1).Scan(&results).Error
	} else {
		err = query.Where("b.user_id = ? AND (b.name LIKE ? OR u.ws_id LIKE ?)", userID, pattern, pattern).Scan(&results).Error
	}

	if err != nil {
		return nil, err
	}

	return results, nil
}

func GetUsersBeneficiaries(db *gorm.DB, userID uint, beneficiaryTypeID uint) (*[]resDTO.BeneficiaryWalletMapDTO, error) {
	var result []resDTO.BeneficiaryWalletMapDTO = make([]resDTO.BeneficiaryWalletMapDTO, 0)
	subQuery := `
	(
		SELECT t1.user_id, t1.amount, t1.txn_date
		FROM transactions t1
		WHERE t1.dr_cr = 'Debit'
		AND t1.txn_date = (
			SELECT MAX(t2.txn_date)
			FROM transactions t2
			WHERE t2.user_id = t1.user_id AND t2.dr_cr = 'Debit'
		)
	) AS last_txn
`
	err := db.Table("beneficiaries AS b").
		Select(`
			b.beneficiary_id AS beneficiary_id,
			b.Name AS beneficiary_name,
			u.ws_id AS ws_id,
			bt.name AS beneficiary_type,
			bt.id as beneficiary_type_id,
			b.is_fav,
			b.created_at,
			last_txn.amount AS last_payment,
		CAST(UNIX_TIMESTAMP(last_txn.txn_date) AS SIGNED) AS last_payment_date
		`).
		Joins("INNER JOIN users u ON b.beneficiary_id = u.id").
		Joins("INNER JOIN beneficiary_types bt ON b.beneficiary_type_id = bt.id").
		Joins("LEFT JOIN "+subQuery+" ON last_txn.user_id = b.beneficiary_id").
		Where("b.user_id = ? AND b.beneficiary_type_id = ?", userID, beneficiaryTypeID).
		Order("b.id DESC").
		Scan(&result).Error

	if err != nil {
		return nil, err
	}
	return &result, nil
}

func GetAllUsersBeneficiaries(db *gorm.DB, userID uint, beneficiaryTypeID uint) (*[]resDTO.BeneficiaryWalletMapDTO, error) {
	var result []resDTO.BeneficiaryWalletMapDTO = make([]resDTO.BeneficiaryWalletMapDTO, 0)

	// err := db.Table("beneficiaries AS b").
	// 	Select(`
	// 		b.beneficiary_id AS beneficiary_id,
	// 		b.Name AS beneficiary_name,
	// 		u.ws_id AS ws_id,
	// 		b.is_fav,
	// 		b.created_at,
	// 		6 as beneficiary_type_id
	// 	`).
	// 	Joins("INNER JOIN users u ON b.beneficiary_id = u.id").
	// 	//Joins("INNER JOIN beneficiary_types bt ON b.beneficiary_type_id = bt.id").
	// 	Where("b.user_id = ? ", userID).
	// 	Order("b.id DESC").
	// 	Scan(&result).Error
	subQuery := `
	(
		SELECT t1.user_id, t1.amount, t1.txn_date
		FROM transactions t1
		WHERE t1.dr_cr = 'Debit'
		AND t1.txn_date = (
			SELECT MAX(t2.txn_date)
			FROM transactions t2
			WHERE t2.user_id = t1.user_id AND t2.dr_cr = 'Debit'
		)
	) AS last_txn
`

	err := db.Table("beneficiaries AS b").
		Select(`
		b.beneficiary_id AS beneficiary_id,
		b.Name AS beneficiary_name,
		u.ws_id AS ws_id,
		b.is_fav,
		6 AS beneficiary_type_id,
		last_txn.amount AS last_payment,
		CAST(UNIX_TIMESTAMP(last_txn.txn_date) AS SIGNED) AS last_payment_date
	`).
		Joins("INNER JOIN users u ON b.beneficiary_id = u.id").
		Joins("LEFT JOIN "+subQuery+" ON last_txn.user_id = b.beneficiary_id").
		Where("b.user_id = ?", userID).
		Order("b.id DESC").
		Scan(&result).Error

	if err != nil {
		return nil, err
	}
	return &result, nil
}

func GetBeneficiaryDetailsFromDB(db *gorm.DB, userID uint, ws_id string) (*resDTO.BeneficiaryDetails, error) {
	var details *resDTO.BeneficiaryDetails // PANIC ISSUE

	err := db.Table("beneficiaries b").
		Select("b.beneficiary_id AS BeneficiaryID , b.name AS Name, u.ws_id AS WSID, w.id AS WalletID,b.created_at as CreatedAt").
		InnerJoins("JOIN users u ON b.beneficiary_id = u.id").
		InnerJoins("JOIN wallets w ON u.id = w.user_id").
		Where("b.user_id = ? AND u.ws_id = ?", userID, ws_id).
		Scan(&details).Error

	if err != nil {
		return nil, err
	}

	return details, nil
}

func getUserDetailsByUserID(db *gorm.DB, userId uint) (*resDTO.UserDetailsDTO, error) {
	var userDetails models.UserDetails
	if err := db.Where("user_id = ?", userId).First(&userDetails).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil // return error as no record found
		}
		return nil, err
	}

	userDetailsDTO := &resDTO.UserDetailsDTO{
		Email:                  userDetails.Email,
		DateOfBirth:            userDetails.DateOfBirth,
		Nationality:            userDetails.Nationality,
		PlaceOfBirth:           userDetails.PlaceOfBirth,
		NIF:                    userDetails.NIF,
		Street:                 userDetails.Street,
		Province:               userDetails.Province,
		PostalCode:             userDetails.PostalCode,
		City:                   userDetails.City,
		Country:                userDetails.Country,
		Occupation:             userDetails.Occupation,
		Company:                userDetails.Company,
		EstimatedIncome:        userDetails.EstimatedIncome,
		SourceOfFunds:          userDetails.SourceOfFunds,
		NicURLFront:            userDetails.NicURLFront,
		NicURLBack:             userDetails.NicURLBack,
		PageIndex:              userDetails.PageIndex,
		IsVerify:               userDetails.IsVerify,
		IsPep:                  userDetails.IsPep,
		IsTaxResidentElsewhere: userDetails.IsTaxResidentElsewhere,
	}

	return userDetailsDTO, nil
}

func GetWalletAmountReceiver(db *gorm.DB, wabisabiId string) (*models.Wallet, error) {
	var wallet models.Wallet
	err := db.Joins("INNER JOIN users u ON u.id = wallets.user_id").
		Where("u.ws_id = ?", wabisabiId).
		First(&wallet).Error

	if err != nil {
		return nil, err
	}
	return &wallet, nil
}

func GetWalletAmountSender(db *gorm.DB, userID uint) (*models.Wallet, error) {
	var wallet models.Wallet
	err := db.Where("user_id = ?", userID).First(&wallet).Error

	if err != nil {
		return nil, err
	}
	return &wallet, nil
}

func InitiateWallettoWalletTransactionDB(db *gorm.DB, transferRequest reqDTO.TransferRequest, senderEmail string) (int, error, *resDTO.DBResDTO) {
	var dbRes resDTO.DBResDTO
	tx := db.Begin()

	senderWallet, err := GetWalletAmountSender(db, transferRequest.FromUserID)
	if err != nil {
		return http.StatusNotFound, fmt.Errorf("sender wallet not found"), nil
	}

	senderAmountStr, err := DecryptAES(senderWallet.Amount)
	if err != nil {
		return http.StatusInternalServerError, fmt.Errorf("error decrypting sender balance"), nil
	}

	senderAmount, err := strconv.ParseFloat(senderAmountStr, 64)
	if err != nil {
		return http.StatusInternalServerError, fmt.Errorf("error parsing sender balance"), nil
	}

	if senderAmount < transferRequest.Amount {
		return http.StatusBadRequest, fmt.Errorf("insufficient balance"), nil
	}

	// get receiver wallet and decrypt amount
	receiverWallet, err := GetWalletAmountReceiver(db, transferRequest.WabisabiID)
	if err != nil {
		return http.StatusNotFound, fmt.Errorf("receiver wallet not found"), nil
	}

	// todo single hit
	var receiverUser models.User
	db.Where("ws_id = ?", transferRequest.WabisabiID).First(&receiverUser)
	dbRes.ReceiverName = receiverUser.FirstName + " " + receiverUser.LastName

	if senderWallet.ID == receiverWallet.ID {
		return http.StatusBadRequest, fmt.Errorf("transfer to your own wallet is not allowed"), nil
	}

	receiverAmountStr, err := DecryptAES(receiverWallet.Amount)
	if err != nil {
		return http.StatusInternalServerError, fmt.Errorf("error decrypting receiver balance"), nil
	}

	receiverAmount, err := strconv.ParseFloat(receiverAmountStr, 64)
	if err != nil {
		return http.StatusInternalServerError, fmt.Errorf("error parsing receiver balance"), nil
	}

	senderAmount -= transferRequest.Amount
	receiverAmount += transferRequest.Amount

	encryptedSenderAmount, err := EncryptAES(fmt.Sprintf("%.4f", senderAmount))
	if err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, fmt.Errorf("failed to encrypt sender balance: %v", err), nil
	}

	encryptedReceiverAmount, err := EncryptAES(fmt.Sprintf("%.4f", receiverAmount))
	if err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, fmt.Errorf("failed to encrypt receiver balance: %v", err), nil
	}

	senderWallet.Amount = encryptedSenderAmount
	if err := tx.Save(&senderWallet).Error; err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, fmt.Errorf("failed to update sender balance: %v", err), nil
	}

	receiverWallet.Amount = encryptedReceiverAmount
	if err := tx.Save(&receiverWallet).Error; err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, fmt.Errorf("failed to update receiver balance: %v", err), nil
	}

	senderTransaction := models.Transaction{
		UserID:        senderWallet.UserID,
		ToFrom:        strconv.Itoa(int(receiverWallet.UserID)),
		Amount:        transferRequest.Amount,
		WalletID:      senderWallet.ID,
		DrCr:          "Debit",
		TxnType:       "wallet_to_wallet",
		Status:        "COMPLETED",
		PaymentTypeId: transferRequest.PaymentTypeId,
	}

	receiverTransaction := models.Transaction{
		UserID:        receiverWallet.UserID,
		ToFrom:        strconv.Itoa(int(senderWallet.UserID)),
		Amount:        transferRequest.Amount,
		WalletID:      receiverWallet.ID,
		DrCr:          "Credit",
		TxnType:       "wallet_to_wallet",
		Status:        "COMPLETED",
		PaymentTypeId: transferRequest.PaymentTypeId,
	}

	if err := tx.Create(&senderTransaction).Error; err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, fmt.Errorf("failed to log sender transaction: %v", err), nil
	}

	if err := tx.Create(&receiverTransaction).Error; err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, fmt.Errorf("failed to log receiver transaction: %v", err), nil
	}
	// After successful transaction, save beneficiary
	if err := SaveBeneficiaryAfterTransfer(db, transferRequest.FromUserID, transferRequest.WabisabiID); err != nil {
		// Log the error but don't fail the transaction
		fmt.Printf("Error saving beneficiary: %v\n", err)
	}
	tx.Commit()

	dbRes.TxnTime = senderTransaction.CreatedAt
	err1 := SendOTPEmail(receiverUser.Email, "Payment received", "Payment received successfully")
	if err1 != nil {

		fmt.Println("Error sending email:", err1)
	}
	err2 := SendOTPEmail(receiverUser.Email, "Payment sent", "Payment has been initiated successfully")
	if err2 != nil {

		fmt.Println("Error sending email:", err2)
	}
	// After successful transaction, broadcast updates to both sender and receiver
	//if err == nil {
	// Broadcast to sender
	// BroadcastUpdate(transferRequest.FromUserID, resDTO.HomePageResponseDTO{
	// 	APIResponse: resDTO.APIResponse{
	// 		IsSuccess: true,
	// 		Message:   "Balance updated",
	// 	},
	// 	Data: resDTO.Data{
	// 		Balance:            senderAmount,
	// 		TransactionHistory: getRecentTransactions(transferRequest.FromUserID),
	// 	},
	// })
	BroadcastNotification(transferRequest.FromUserID, "wallet_transfer",
		"Transfer Sent",
		fmt.Sprintf("You sent %v USD to %s", transferRequest.Amount, receiverUser.FirstName),
		map[string]interface{}{
			"amount":         transferRequest.Amount,
			"recipient":      receiverUser.FirstName + " " + receiverUser.LastName,
			"transaction_id": senderTransaction.ID,
		})
	// Broadcast to receiver

	// BroadcastUpdate(receiverWallet.UserID, resDTO.HomePageResponseDTO{
	// 	APIResponse: resDTO.APIResponse{
	// 		IsSuccess: true,
	// 		Message:   "Balance updated",
	// 	},
	// 	Data: resDTO.Data{
	// 		Balance:            receiverAmount,
	// 		TransactionHistory: getRecentTransactions(receiverWallet.UserID),
	// 	},
	// })

	BroadcastNotification(receiverWallet.UserID, "wallet_transfer",
		"Payment Received",
		fmt.Sprintf("You received %v USD from %s", transferRequest.Amount, senderEmail),
		map[string]interface{}{
			"amount": transferRequest.Amount,
		})
	return http.StatusOK, nil, &dbRes
}

func TransactionHistoryDetails(userID uint, startDate, endDate string) ([]resDTO.DateTransactionGroup, error) {
	rawGroups := make([]resDTO.RawTransactionGroup, 0)

	err := config.DB.
		Table("transactions t").
		Select(`
			CAST(t.txn_date AS DATE) as date,
			JSON_ARRAYAGG(
				JSON_OBJECT(
					'id', t.id,
					'user_id', t.user_id,
					'to_from', t.to_from,
					'wallet_id', t.wallet_id,
					'txn_type', t.txn_type,
					'dr_cr', t.dr_cr,
					'amount', t.amount,
					'status', t.status,
					'txn_date', CAST(UNIX_TIMESTAMP(t.txn_date) AS SIGNED),
					'created_at', CAST(UNIX_TIMESTAMP(t.created_at) AS SIGNED),
					'updated_at', CAST(UNIX_TIMESTAMP(t.updated_at) AS SIGNED),
					'deleted_at', CASE 
						WHEN t.deleted_at IS NULL THEN NULL 
						ELSE CAST(UNIX_TIMESTAMP(t.deleted_at) AS SIGNED)
					END,
					'sender_ws_id', sender.ws_id,
					'sender_name', CONCAT(sender.first_name, ' ', sender.last_name),
					'receiver_ws_id', receiver.ws_id,
					'receiver_name', CONCAT(receiver.first_name, ' ', receiver.last_name),
					'purpose', pt.purpose,
					 'icon', pt.icon,
					'brand', CASE 
								WHEN t.txn_type = 'card_top_up' THEN spm.brand 
								ELSE '' 
							END,
					'last4', CASE 
								WHEN t.txn_type = 'card_top_up' THEN spm.last4 
								ELSE '' 
							END,
					'card_icon', CASE 
								WHEN t.txn_type = 'card_top_up' THEN spm.card_icon
								ELSE '' 
							END
				)
			) AS transactions
		`).
		Joins("JOIN users sender ON sender.id = t.user_id").
		Joins("LEFT JOIN users receiver ON receiver.id = t.to_from").
		Joins("LEFT JOIN payment_types pt ON pt.id = t.payment_type_id").
		Joins("LEFT JOIN stripe_payment_methods spm ON spm.id = (SELECT id FROM stripe_payment_methods WHERE user_id = t.user_id ORDER BY created_at DESC LIMIT 1)").
		Where("(t.user_id = ?) AND cast(t.txn_date as date) BETWEEN ? AND ?", userID, startDate, endDate).
		Group("CAST(t.txn_date AS DATE)").
		Order("date DESC").
		Scan(&rawGroups).Error

	if err != nil {
		return nil, err
	}

	if len(rawGroups) == 0 {
		return nil, nil
	}

	var result []resDTO.DateTransactionGroup
	for _, group := range rawGroups {
		var txns []resDTO.RecentTransactionsNew
		if err := json.Unmarshal([]byte(group.Transactions), &txns); err != nil {
			log.Println("JSON unmarshal error for group:", group.Date, err)
			continue
		}

		// Convert MySQL date string to Unix timestamp (midnight UTC)
		dateUnixTimestamp, err := convertDateStringToUnixTimestamp(group.Date)
		if err != nil {
			log.Println("Date conversion error for group:", group.Date, err)
			continue
		}

		result = append(result, resDTO.DateTransactionGroup{
			Date:        dateUnixTimestamp, // Now converted to Unix timestamp
			TransDetail: txns,
		})
	}

	return result, nil
}

// Helper function to get recent transactions
func GetRecentTransactions(userID uint) []resDTO.RecentTransactions {
	// var transactionHistory []models.Transaction
	// config.DB.Where("user_id = ?", userID).Order("created_at desc").Limit(10).Find(&transactionHistory)

	var transactionHistory []resDTO.RecentTransactions

	// err := config.DB.
	// 	Table("transactions t").
	// 	Select(`t.*, u.ws_id, CONCAT(u.first_name, ' ', u.last_name) AS full_name, pt.purpose, pt.icon`).
	// 	Joins("JOIN users u ON u.id = t.user_id").
	// 	Joins("LEFT JOIN payment_types pt ON t.payment_type_id = pt.id").
	// 	Where("t.to_from = ?", userID).
	// 	Order("t.created_at DESC").
	// 	Limit(10).
	// 	Scan(&transactionHistory).Error
	// spm.icon
	err := config.DB.
		Table("transactions t").
		Select(`
		t.user_id, t.to_from,t.wallet_id, t.txn_type,
		sender.ws_id AS sender_ws_id,  t.dr_cr,t.amount, t.status,
		CAST(UNIX_TIMESTAMP(CONVERT_TZ(t.txn_date, '+05:00', '+00:00')) AS SIGNED) as txn_date ,
		CONCAT(sender.first_name, ' ', sender.last_name) AS sender_name, 
		receiver.ws_id AS receiver_ws_id, 
		CONCAT(receiver.first_name, ' ', receiver.last_name) AS receiver_name,
		pt.purpose, 
		pt.icon,
		CASE 
			WHEN t.txn_type = 'card_top_up' THEN spm.brand 
			ELSE '' 
		END AS brand,
		CASE 
			WHEN t.txn_type = 'card_top_up' THEN spm.last4 
			ELSE '' 
		END AS last4,
		'card_icon', CASE 
			WHEN t.txn_type = 'card_top_up' THEN spm.card_icon
			ELSE '' 
		END AS CardIcon
	`).
		Joins("JOIN users sender ON sender.id = t.user_id").
		Joins("LEFT JOIN users receiver ON receiver.id = t.to_from").
		Joins("LEFT JOIN payment_types pt ON pt.id = t.payment_type_id").
		Joins("LEFT JOIN stripe_payment_methods spm ON spm.id = (SELECT id FROM stripe_payment_methods WHERE user_id = t.user_id ORDER BY created_at DESC LIMIT 1)").
		Where("t.user_id =?", userID).
		//("t.user_id = ? OR t.to_from = ?", userID, userID).
		Order("t.created_at DESC").
		Limit(10).
		Scan(&transactionHistory).Error

	if err != nil {
		log.Println("Error fetching transactions:", err)
	}

	var groupedTxn []resDTO.RecentTransactions = make([]resDTO.RecentTransactions, 0)
	for _, txn := range transactionHistory {
		groupedTxn = append(groupedTxn, resDTO.RecentTransactions{
			UserID:       txn.UserID,
			WalletID:     txn.WalletID,
			DrCr:         txn.DrCr,
			TxnDate:      txn.TxnDate,
			TxnType:      txn.TxnType,
			ToFrom:       txn.ToFrom,
			Amount:       txn.Amount,
			Status:       txn.Status,
			Purpose:      txn.Purpose,
			Icon:         txn.Icon,
			SenderWsID:   txn.SenderWsID,
			SenderName:   txn.SenderName,
			ReceiverWsID: txn.ReceiverWsID,
			ReceiverName: txn.ReceiverName,
			Brand:        txn.Brand,
			Last4:        txn.Last4,
			CardIcon:     txn.CardIcon,
		})
	}
	return groupedTxn
}

func InitiateTopUpTransactionDB(db *gorm.DB, transferRequest reqDTO.TransferRequest) (int, error) {
	tx := db.Begin()
	purposeID, _ := GetPaymentTypeIDByPurpose(db, "Top Up")
	userWallet, err := GetWalletAmountSender(db, transferRequest.FromUserID)
	if err != nil {
		tx.Rollback()
		return http.StatusNotFound, fmt.Errorf("user wallet not found")
	}

	userWalletStr, err := DecryptAES(userWallet.Amount)
	if err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, fmt.Errorf("error decrypting sender balance")
	}

	userAmount, err := strconv.ParseFloat(userWalletStr, 64)
	if err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, fmt.Errorf("error parsing sender balance")
	}

	userAmount += transferRequest.Amount

	encryptedUserAmount, err := EncryptAES(fmt.Sprintf("%.4f", userAmount))
	if err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, fmt.Errorf("failed to encrypt sender balance: %v", err)
	}

	userWallet.Amount = encryptedUserAmount
	if err := tx.Save(&userWallet).Error; err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, fmt.Errorf("failed to update sender balance: %v", err)
	}

	userTransaction := models.Transaction{
		UserID:        userWallet.UserID,
		ToFrom:        strconv.Itoa(int(userWallet.UserID)),
		Amount:        transferRequest.Amount,
		WalletID:      userWallet.ID,
		DrCr:          "Credit",
		TxnType:       "card_top_up",
		Status:        "COMPLETED",
		PaymentTypeId: purposeID,
	}

	if err := tx.Create(&userTransaction).Error; err != nil {
		tx.Rollback()
		return http.StatusInternalServerError, fmt.Errorf("failed to log sender transaction: %v", err)
	}

	tx.Commit()
	// BroadcastUpdate(transferRequest.FromUserID, resDTO.HomePageResponseDTO{
	// 	APIResponse: resDTO.APIResponse{
	// 		IsSuccess: true,
	// 		Message:   "Balance updated",
	// 	},
	// 	Data: resDTO.Data{
	// 		Balance:            userAmount,
	// 		TransactionHistory: getRecentTransactions(transferRequest.FromUserID),
	// 	},
	// })
	BroadcastNotification(transferRequest.FromUserID, "topup",
		"Top-up Successful",
		fmt.Sprintf("Your wallet has been topped up with %v USD", transferRequest.Amount),
		map[string]interface{}{
			"amount": transferRequest.Amount,
			"method": "card",
		})
	return http.StatusOK, nil
}

func getUserByID(db *gorm.DB, userID uint) (*models.User, error) {
	var user models.User
	if err := db.Where("id = ?", userID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &user, nil
}

func GetUserTransactionHistory(db *gorm.DB, userID uint) ([]resDTO.TransactionHistory, error) {
	var transactionHistory []resDTO.TransactionHistory
	db.Table("transactions t").
		Select("t.user_id, b.name, b.bank_name, t.wallet_id, t.created_at, t.dr_cr, t.amount").
		Joins("left join beneficiaries b on b.user_id = t.user_id").
		Where("t.user_id = ?", userID).
		Order("t.created_at desc").
		Scan(&transactionHistory)
	return transactionHistory, nil
}

func CheckStripeCustomer(db *gorm.DB, userId uint) (*models.User, error) {
	var user models.User
	if err := db.Where("id = ?", userId).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return &user, nil // No record found, return nil instead of error
		}
		return nil, err // Return actual database error
	}

	return &user, nil
}

// GetUserPaymentMethod retrieves the user's payment method ID from the database
func GetUserPaymentMethod(db *gorm.DB, userID uint) (string, error) {
	var paymentMethod models.StripePaymentMethod
	if err := db.Where("user_id = ?", userID).First(&paymentMethod).Error; err != nil {
		return "", err
	}

	if paymentMethod.MethodToken == "" {
		return "", fmt.Errorf("no payment method found for user")
	}

	return paymentMethod.MethodToken, nil
}

func InsertStripeCustomerId(db *gorm.DB, stripeId string, userID uint) error {
	err := db.Model(&models.User{}).Where("id = ?", userID).Update("stripe_customer_id", stripeId).Error
	if err != nil {
		return err // Return error if update fails
	}
	return nil
}

func InsertOnfidoApplicantId(db *gorm.DB, user *models.User, onfidoApplicantId string, userID uint) error {
	err := db.Model(&models.User{}).Where("id = ?", userID).Update("onfido_applicant_id", onfidoApplicantId).Error
	if err != nil {
		return err // Return error if update fails
	}
	return nil
}

// TODO: apply this to all userUpdateFunctions
func UpdateUserByParam(db *gorm.DB, column string, value interface{}, updates map[string]interface{}) error {
	return db.Model(&models.User{}).Where(fmt.Sprintf("%s = ?", column), value).Updates(updates).Error
}

func GetUserContacts(db *gorm.DB, validContacts map[string]NumberParts, userID uint) ([]resDTO.Contact, error) {
	if len(validContacts) == 0 {
		return []resDTO.Contact{}, nil
	}

	var contactPairs []string
	var args []interface{}

	for _, contact := range validContacts {
		contactPairs = append(contactPairs, "(phone_number = ? AND phone_country_code = ?)")
		args = append(args, contact.CoreNumber, contact.CountryCode)
	}

	registeredUsers := make([]models.User, 0)
	err := db.Model(&models.User{}).Where("id != ?", userID).Where(strings.Join(contactPairs, " OR "), args...).Find(&registeredUsers).Error
	if err != nil {
		return nil, fmt.Errorf("database query error: %w", err)
	}

	filteredContacts := make([]resDTO.Contact, 0)
	for _, user := range registeredUsers {
		// Find the original input format for this user
		originalNumber := ""
		for originalInput, parsedContact := range validContacts {
			if parsedContact.CoreNumber == user.PhoneNumber && parsedContact.CountryCode == user.PhoneCountryCode {
				originalNumber = originalInput
				break
			}
		}

		if originalNumber != "" {
			_, isOnline := bgHub.GetBackroundConnection(user.Id)
			filteredContacts = append(filteredContacts, resDTO.Contact{
				Number:       originalNumber, // Return in original format
				Name:         user.FirstName + " " + user.LastName,
				Id:           user.Id,
				WsId:         user.WSID,
				IsOnline:     isOnline,
				ProfileImage: "",
			})
		}
	}

	return filteredContacts, nil
}

func SaveMessage(db *gorm.DB, senderID, receiverID uint, content string, status string) error {
	msg := models.Message{
		SenderID:    senderID,
		MessageType: "text",
	}

	tx := db.Begin()


	if err := tx.Create(&msg).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("def")
	}

	// Create text message with content
	textMsg := models.TextMessage{
		MessageID: msg.ID,
		Content:   content,
	}

	if err := tx.Create(&textMsg).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("abc")
	}

	return tx.Commit().Error
}

func findOrCreateConversation(sender, receiver uint) (uint, error) {
	var conversation models.Conversation
	err := config.DB.Raw(`
		SELECT c.* FROM conversations c
		JOIN conversation_participants cp1 ON c.id = cp1.conversation_id
		JOIN conversation_participants cp2 ON c.id = cp2.conversation_id
		WHERE c.is_group = FALSE 
		AND cp1.user_id = ?
		AND cp2.user_id = ?;`, sender, receiver).First(&conversation).Error

	if err == nil {
		return conversation.ID, nil
	}

	// Create new conversation
	tx := config.DB.Begin()

	var receiverUser models.User
	err = config.DB.Where("id = ?", sender).First(&receiverUser).Error
	if err != nil {
		tx.Rollback()
		return 0, err
	}

	// TODO project record creation
	newConversation := models.Conversation{
		IsGroup:   false,
		CreatedBy: sender,
		Name: receiverUser.FirstName + " " + receiverUser.LastName,
	}

	if err := tx.Create(&newConversation).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	participants := []models.ConversationParticipant{
		{ConversationID: newConversation.ID, UserID: sender},
		{ConversationID: newConversation.ID, UserID: receiver},
	}

	if err := tx.Create(&participants).Error; err != nil {
		tx.Rollback()
		return 0, err
	}

	if err := tx.Commit().Error; err != nil {
		return 0, err
	}

	return newConversation.ID, nil
}

func GetUserConversationsFromDB(userID uint) ([]resDTO.UserConversationDTO, error) {
	results := make([]resDTO.UserConversationDTO, 0)

	err := config.DB.Raw(`SELECT 
							cp1.user_id,
							c.id as conversation_id,
							c.is_group,
							COALESCE(c.name, u2.first_name) AS conversation_name,
							MAX(m.sent_at) AS last_message_time,
							SUBSTRING_INDEX(GROUP_CONCAT(m.content ORDER BY m.sent_at DESC), ',', 1) AS last_message
						FROM conversations c
						JOIN conversation_participants cp1 ON c.id = cp1.conversation_id
						JOIN users u1 ON cp1.user_id = u1.id
						LEFT JOIN conversation_participants cp2 ON c.id = cp2.conversation_id AND cp2.user_id != cp1.user_id
						LEFT JOIN users u2 ON u2.id = cp2.user_id AND c.is_group = FALSE
						LEFT JOIN messages m ON c.id = m.conversation_id
						WHERE cp1.user_id = ?
						GROUP BY cp1.user_id, c.id, c.is_group, c.name, u2.first_name
						ORDER BY last_message_time DESC;`, userID).Scan(&results).Error

	return results, err
}

// GetMessage retrieves a specific message with its content based on type
func GetMessage(db *gorm.DB, messageID uint) (resDTO.MessageDTO, error) {
	var message models.Message
	var responseDTO resDTO.MessageDTO
	err := db.First(&message, messageID).Error
	if err != nil {
		return responseDTO, fmt.Errorf("error 1")
	}

	// Set base message properties
	responseDTO = resDTO.MessageDTO{
		ID:          message.ID,
		SenderID:    uint(message.SenderID),
		MessageType: message.MessageType,
		CreatedAt:   message.CreatedAt,
	}

	// Get type-specific content
	switch message.MessageType {
	case "text":
		var textMessage models.TextMessage
		if err := db.Where("message_id = ?", messageID).First(&textMessage).Error; err != nil {
			return responseDTO, fmt.Errorf("error 2")
		}
		responseDTO.Content = textMessage.Content

	case "agreement":
		var agreement models.Agreement
		if err := db.Where("message_id = ?", messageID).First(&agreement).Error; err != nil {
			return responseDTO, fmt.Errorf("error 3")
		}
		responseDTO.AgreementID = agreement.MessageID
		responseDTO.AgreementTitle = agreement.Title
	}

	return responseDTO, nil
}

func UpdateKYCVerificationStatus(db *gorm.DB, verificationSessionID string, updates map[string]interface{}) error {
	return db.Model(&models.KYCVerification{}).
		Where("verification_session_id = ?", verificationSessionID).
		Updates(updates).Error
}

func SaveBeneficiaryAfterTransfer(db *gorm.DB, fromUserID uint, receiverWSID string) error {
	// Get receiver's user ID from WSID
	var receiverUser models.User
	if err := db.Where("ws_id = ?", receiverWSID).First(&receiverUser).Error; err != nil {
		return err
	}

	// Check if beneficiary already exists
	var existing models.Beneficiaries
	if err := db.Where("user_id = ? AND beneficiary_id = ?", fromUserID, receiverUser.Id).First(&existing).Error; err == nil {
		// Beneficiary already exists, no need to create
		return nil
	}

	// Create new beneficiary
	newBeneficiary := models.Beneficiaries{
		UserID:        fromUserID,
		BeneficiaryID: receiverUser.Id,
		Name:          receiverUser.FirstName + " " + receiverUser.LastName,
	}

	return db.Create(&newBeneficiary).Error
}

func fetchSavedCards(userID uint) ([]resDTO.CardInfo, error) {
	// Fetch updated saved cards
	var rawCards []models.StripePaymentMethod
	err := config.DB.Where("user_id = ?", userID).Order("is_default DESC, created_at DESC").Find(&rawCards).Error
	if err != nil {
		return nil, fmt.Errorf("database error: %v", err)
	}

	var cards []resDTO.CardInfo = make([]resDTO.CardInfo, 0)
	for _, card := range rawCards {
		cards = append(cards, resDTO.CardInfo{
			Brand:           card.Brand,
			Last4:           card.Last4,
			ExpMonth:        int64(card.ExpMonth),
			ExpYear:         int64(card.ExpYear),
			PaymentMethodID: card.MethodToken,
			IsDefault:       card.IsDefault,
			CardIcon:        card.CardIcon,
			CardUserName:    card.CardUserName,
		})
	}

	return cards, nil
}

func GetUserConnectedAccount(db *gorm.DB, userID uint) (*models.StripeConnectedAccount, error) {
	var connectedAccount models.StripeConnectedAccount
	result := db.Where("user_id = ?", userID).First(&connectedAccount)

	if result.Error != nil {
		return nil, result.Error
	}

	return &connectedAccount, nil
}

func CreateStripePaymentMethodDB(db *gorm.DB, paymentMethod *models.StripePaymentMethod) error {
	if err := db.Create(&paymentMethod).Error; err != nil {
		return err
	}
	return nil
}

func CheckUserConnectedAccount(db *gorm.DB, userID uint) (*models.StripeConnectedAccount, error) {
	var connectedAccount models.StripeConnectedAccount
	err := db.Where("user_id = ?", userID).First(&connectedAccount).Error
	return &connectedAccount, err
}

func GetUserKYCVerificationSession(db *gorm.DB, userID uint) (*models.KYCVerification, error) {
	var verificationSession models.KYCVerification
	err := db.Where("user_id = ?", userID).First(&verificationSession).Error
	return &verificationSession, err
}

func CreateStripeConnectedAccountRecord(db *gorm.DB, record *models.StripeConnectedAccount) error {
	return db.Create(record).Error
}

func GetPaymentTypesFromDB(db *gorm.DB) ([]models.PaymentType, error) {
	var paymentMethods []models.PaymentType
	err := db.Find(&paymentMethods).Error
	return paymentMethods, err
}

func GetBeneficiaryTypesFromDB(db *gorm.DB) ([]models.BeneficiaryType, error) {
	var beneficiaryTypes []models.BeneficiaryType
	err := db.Find(&beneficiaryTypes).Error
	return beneficiaryTypes, err
}

func GetPaymentTypeIDByPurpose(db *gorm.DB, purpose string) (uint, error) {
	var paymentMethod models.PaymentType
	if err := db.Where("purpose = ?", purpose).First(&paymentMethod).Error; err != nil {
		return 0, err
	}
	return paymentMethod.Id, nil
}

func UpdateBeneficiaryTypeDB(db *gorm.DB, beneficiaryID, beneficiaryTypeID uint, isfav *bool, userID uint) error {
	updates := map[string]interface{}{}
	if isfav != nil {
		updates["is_fav"] = isfav
	} else {
		updates["beneficiary_type_id"] = beneficiaryTypeID
	}
	// updates := map[string]interface{}{
	// 	"beneficiary_type_id": beneficiaryTypeID,
	// }

	return db.Model(&models.Beneficiaries{}).
		Where("beneficiary_id = ? and user_id= ?", beneficiaryID, userID).
		Updates(updates).Error
	//Update("beneficiary_type_id", beneficiaryTypeID).Error
}

//We are using fetched card details
// func GetCardDetailsByUserID(db *gorm.DB, userID uint) (*resDTO.CardInfo, error) {
// 	var card resDTO.CardInfo
// 	err := db.Raw(`
//         SELECT
//             brand,
//             method_token as payment_method_id,
//             last4,
//             exp_month,
//             exp_year,
//             is_default,
// 			card_icon,
// 			card_user_name
//         FROM stripe_payment_methods
//         WHERE user_id = ? AND is_default = 1
//         LIMIT 1
//     `, userID).Scan(&card).Error

// 	if err != nil {
// 		return nil, err
// 	}

// 	return &card, nil
// }

func findUserbyWsId(wsid string) (*models.User, error) {
	// Find the user using WSID
	var receiver models.User
	if err := config.DB.Where("ws_id = ?", wsid).First(&receiver).Error; err != nil {
		return nil, err
	}
	return &receiver, nil
}

func checkBeneficiaryExistsAlready(userID, receiverID uint) bool {
	// Check if beneficiary already exists
	var existing models.Beneficiaries
	if err := config.DB.Where("user_id = ? AND beneficiary_id = ?", userID, receiverID).First(&existing).Error; err == nil {
		return true
	}
	return false
}

func createNewBeneficiary(userID uint, req reqDTO.AddBeneficiaryRequestDTO, receiver models.User) error {
	// Save new beneficiary
	newBeneficiary := models.Beneficiaries{
		UserID:            userID,
		BeneficiaryID:     receiver.Id,
		Name:              receiver.FirstName + " " + receiver.LastName,
		BeneficiaryTypeID: req.BeneficiaryTypeId,
		IsFav:             req.IsFav,
	}
	if err := config.DB.Create(&newBeneficiary).Error; err != nil {
		return err
	}

	return nil
}

// DeleteBeneficiary deletes a beneficiary for a specific user
func DeleteBeneficiaryDB(userID uint, beneficiaryID uint) error {

	// FIrst check if the beneficiary even exists for the user
	if !checkBeneficiaryExistsAlready(userID, beneficiaryID) {
		return fmt.Errorf("beneficiary not found or does not belong to user")
	}

	// Delete the beneficiary
	err := config.DB.Where("user_id = ? AND beneficiary_id = ?", userID, beneficiaryID).
		Delete(&models.Beneficiaries{}).Error

	if err != nil {
		return fmt.Errorf("error deleting beneficiary: %v", err)
	}

	return nil
}

func getUserFavBeneficiaries(userID uint) ([]resDTO.BeneficiaryWalletMapDTO, error) {
	var result []resDTO.BeneficiaryWalletMapDTO = make([]resDTO.BeneficiaryWalletMapDTO, 0)
	err := config.DB.Table("beneficiaries AS b").
		Select(`
			b.beneficiary_id AS beneficiary_id,
			b.Name AS beneficiary_name,
			u.ws_id AS ws_id,
			bt.name AS beneficiary_type,
			b.created_at,
			b.is_fav 
		`).
		Joins("INNER JOIN users u ON b.beneficiary_id = u.id").
		Joins("INNER JOIN beneficiary_types bt ON b.beneficiary_type_id = bt.id").
		Where("b.user_id = ? AND b.is_fav = ?", userID, true).
		Order("b.id DESC").
		Scan(&result).Error

	if err != nil {
		return nil, err
	}

	return result, nil
}

func getGraphKeysDB() ([]models.GraphKeys, error) {
	var result []models.GraphKeys
	err := config.DB.Find(&result).Error
	if err != nil {
		return nil, err
	}
	return result, err
}

func getValueofGraphKeyId(id string) (string, error) {
	uintID, err := strconv.ParseUint(id, 10, 32)
	if err != nil {
		return "", err
	}

	var key models.GraphKeys
	result := config.DB.First(&key, uint(uintID))
	if result.Error != nil {
		return "", result.Error
	}

	return key.KeyValue, nil
}

// dont delete, returns timestamps in PKT strings
func TransactionHistoryDetailsGraphForSingleDayPKT(userID uint, startDate, endDate string) ([]resDTO.AggregatedGraphData, []resDTO.GraphTransactionNew, error) {
	rawGroups := make([]resDTO.RawTransactionGroup, 0)
	graphData := make([]resDTO.AggregatedGraphData, 0)
	allTransactions := make([]resDTO.GraphTransactionNew, 0)

	startTimestamp := startDate + " 00:00:00.000"
	endTimestamp := endDate + " 23:59:59.999"

	err := config.DB.
		Table("transactions t").
		Select(`
			t.txn_date as date,
			JSON_ARRAYAGG(
				JSON_OBJECT(
					'id', t.id,
					'user_id', t.user_id,
					'to_from', t.to_from,
					'wallet_id', t.wallet_id,
					'txn_type', t.txn_type,
					'dr_cr', t.dr_cr,
					'amount', t.amount,
					'status', t.status,
					'txn_date', t.txn_date,
					'created_at', t.created_at,
					'updated_at', t.updated_at,
					'deleted_at', t.deleted_at,
					'sender_ws_id', sender.ws_id,
					'sender_name', CONCAT(sender.first_name, ' ', sender.last_name),
					'receiver_ws_id', receiver.ws_id,
					'receiver_name', CONCAT(receiver.first_name, ' ', receiver.last_name),
					'purpose', pt.purpose,
					'icon', pt.icon,
					'brand', CASE 
								WHEN t.txn_type = 'card_top_up' THEN spm.brand 
								ELSE '' 
							END,
					'last4', CASE 
								WHEN t.txn_type = 'card_top_up' THEN spm.last4 
								ELSE '' 
							END,
					'card_icon', CASE 
								WHEN t.txn_type = 'card_top_up' THEN spm.card_icon
								ELSE '' 
							END
				)
			) AS transactions
		`).
		Joins("JOIN users sender ON sender.id = t.user_id").
		Joins("LEFT JOIN users receiver ON receiver.id = t.to_from").
		Joins("LEFT JOIN payment_types pt ON pt.id = t.payment_type_id").
		Joins("LEFT JOIN stripe_payment_methods spm ON spm.id = (SELECT id FROM stripe_payment_methods WHERE user_id = t.user_id ORDER BY created_at DESC LIMIT 1)").
		Where("(t.user_id = ?) AND txn_date BETWEEN ? AND ?", userID, startTimestamp, endTimestamp).
		Group("date").
		Order("date DESC").
		Scan(&rawGroups).Error

	if err != nil {
		return graphData, allTransactions, err
	}

	if len(rawGroups) == 0 {
		return graphData, allTransactions, nil
	}

	for _, group := range rawGroups {
		txns := make([]resDTO.GraphTransactionNew, 0)
		if err := json.Unmarshal([]byte(group.Transactions), &txns); err != nil {
			log.Println("JSON unmarshal error for group:", group.Date, err)
			continue
		}

		for _, txn := range txns {
			txnTime, err := time.Parse("2006-01-02 15:04:05", txn.TxnDate)
			if err != nil {
				txnTime, err = time.Parse("2006-01-02 15:04:05.000000", txn.TxnDate)
				if err != nil {
					log.Println("Error parsing transaction date:", err)
					continue
				}
			}

			standardFormat := txnTime.Format("2006-01-02 15:04:05")

			graphData = append(graphData, resDTO.AggregatedGraphData{
				Key:    standardFormat,
				Amount: txn.Amount,
			})

			txn.TxnDate = standardFormat

			allTransactions = append(allTransactions, txn)
		}
	}

	return graphData, allTransactions, nil
}

// dont remove, this function shows daily aggregated amount in graph_data
func TransactionHistoryDetailsGraphFor7Days(userID uint, startDate, endDate string) ([]resDTO.AggregatedGraphData, []resDTO.GraphTransactionNew, error) {
	rawGroups := make([]resDTO.RawTransactionGroup, 0)
	graphData := make([]resDTO.AggregatedGraphData, 0)
	allTransactions := make([]resDTO.GraphTransactionNew, 0)

	startTimestamp := startDate + " 00:00:00.000"
	endTimestamp := endDate + " 23:59:59.999"

	err := config.DB.
		Table("transactions t").
		Select(`
			CAST(txn_date AS DATE) AS date,
			JSON_ARRAYAGG(
				JSON_OBJECT(
					'id', t.id,
					'user_id', t.user_id,
					'to_from', t.to_from,
					'wallet_id', t.wallet_id,
					'txn_type', t.txn_type,
					'dr_cr', t.dr_cr,
					'amount', t.amount,
					'status', t.status,
					'txn_date', t.txn_date,
					'created_at', t.created_at,
					'updated_at', t.updated_at,
					'deleted_at', t.deleted_at,
					'sender_ws_id', sender.ws_id,
					'sender_name', CONCAT(sender.first_name, ' ', sender.last_name),
					'receiver_ws_id', receiver.ws_id,
					'receiver_name', CONCAT(receiver.first_name, ' ', receiver.last_name),
					'purpose', pt.purpose,
					'icon', pt.icon,
					'brand', CASE 
								WHEN t.txn_type = 'card_top_up' THEN spm.brand 
								ELSE '' 
							END,
					'last4', CASE 
								WHEN t.txn_type = 'card_top_up' THEN spm.last4 
								ELSE '' 
							END,
					'card_icon', CASE 
								WHEN t.txn_type = 'card_top_up' THEN spm.card_icon
								ELSE '' 
							END
				)
			) AS transactions
		`).
		Joins("JOIN users sender ON sender.id = t.user_id").
		Joins("LEFT JOIN users receiver ON receiver.id = t.to_from").
		Joins("LEFT JOIN payment_types pt ON pt.id = t.payment_type_id").
		Joins("LEFT JOIN stripe_payment_methods spm ON spm.id = (SELECT id FROM stripe_payment_methods WHERE user_id = t.user_id ORDER BY created_at DESC LIMIT 1)").
		Where("(t.user_id = ?) AND CAST(txn_date AS DATE) BETWEEN ? AND ?", userID, startTimestamp, endTimestamp).
		Group("CAST(txn_date AS DATE)").
		Order("CAST(txn_date AS DATE) DESC").
		Scan(&rawGroups).Error

	if err != nil {
		return graphData, allTransactions, err
	}

	if len(rawGroups) == 0 {
		return graphData, allTransactions, nil
	}

	for _, group := range rawGroups {
		txns := make([]resDTO.GraphTransactionNew, 0)
		if err := json.Unmarshal([]byte(group.Transactions), &txns); err != nil {
			log.Println("JSON unmarshal error for group:", group.Date, err)
			continue
		}

		var dailyTotal float64
		for _, txn := range txns {
			if txn.DrCr == "CR" {
				dailyTotal += txn.Amount
			} else {
				dailyTotal -= txn.Amount
			}

			parsedTime, err := time.Parse("2006-01-02 15:04:05.000000", txn.TxnDate)
			if err == nil {
				txn.TxnDate = parsedTime.Format("2006-01-02 15:04:05")
			}

			allTransactions = append(allTransactions, txn)
		}

		graphData = append(graphData, resDTO.AggregatedGraphData{
			Key:    group.Date.Format("2006-01-02"),
			Amount: dailyTotal,
		})
	}

	return graphData, allTransactions, nil
}

// returns timestamps in UNIX
func TransactionHistoryDetailsGraphForSingleDayUnix(userID uint, startDate, endDate string, pktLoc *time.Location) ([]resDTO.AggregatedGraphDataUnix, []resDTO.GraphTransactionUnix, error) {
	rawGroups := make([]resDTO.RawTransactionGroup, 0)
	graphData := make([]resDTO.AggregatedGraphDataUnix, 0)
	allTransactions := make([]resDTO.GraphTransactionUnix, 0)

	startTimestamp := startDate + " 00:00:00.000"
	endTimestamp := endDate + " 23:59:59.999"

	err := config.DB.
		Table("transactions t").
		Select(`
			t.txn_date as date,
			JSON_ARRAYAGG(
				JSON_OBJECT(
					'id', t.id,
					'user_id', t.user_id,
					'to_from', t.to_from,
					'wallet_id', t.wallet_id,
					'txn_type', t.txn_type,
					'dr_cr', t.dr_cr,
					'amount', t.amount,
					'status', t.status,
					'txn_date', t.txn_date,
					'created_at', t.created_at,
					'updated_at', t.updated_at,
					'deleted_at', t.deleted_at,
					'sender_ws_id', sender.ws_id,
					'sender_name', CONCAT(sender.first_name, ' ', sender.last_name),
					'receiver_ws_id', receiver.ws_id,
					'receiver_name', CONCAT(receiver.first_name, ' ', receiver.last_name),
					'purpose', pt.purpose,
					'icon', pt.icon,
					'brand', CASE 
								WHEN t.txn_type = 'card_top_up' THEN spm.brand 
								ELSE '' 
							END,
					'last4', CASE 
								WHEN t.txn_type = 'card_top_up' THEN spm.last4 
								ELSE '' 
							END,
					'card_icon', CASE 
								WHEN t.txn_type = 'card_top_up' THEN spm.card_icon
								ELSE '' 
							END
				)
			) AS transactions
		`).
		Joins("JOIN users sender ON sender.id = t.user_id").
		Joins("LEFT JOIN users receiver ON receiver.id = t.to_from").
		Joins("LEFT JOIN payment_types pt ON pt.id = t.payment_type_id").
		Joins("LEFT JOIN stripe_payment_methods spm ON spm.id = (SELECT id FROM stripe_payment_methods WHERE user_id = t.user_id ORDER BY created_at DESC LIMIT 1)").
		Where("(t.user_id = ?) AND txn_date BETWEEN ? AND ?", userID, startTimestamp, endTimestamp).
		Group("date").
		Order("date DESC").
		Scan(&rawGroups).Error

	if err != nil {
		return graphData, allTransactions, err
	}

	if len(rawGroups) == 0 {
		return graphData, allTransactions, nil
	}

	for _, group := range rawGroups {
		txns := make([]resDTO.GraphTransactionNew, 0)
		if err := json.Unmarshal([]byte(group.Transactions), &txns); err != nil {
			log.Println("JSON unmarshal error for group:", group.Date, err)
			continue
		}

		for _, txn := range txns {
			// Parse transaction date - treating it as PKT time since DB stores in PKT
			txnTime, err := time.ParseInLocation("2006-01-02 15:04:05", txn.TxnDate, pktLoc)
			if err != nil {
				txnTime, err = time.ParseInLocation("2006-01-02 15:04:05.000000", txn.TxnDate, pktLoc)
				if err != nil {
					log.Println("Error parsing transaction date:", err)
					continue
				}
			}

			// Parse created_at - treating it as PKT time since DB stores in PKT
			createdAtTime, err := time.ParseInLocation("2006-01-02 15:04:05", txn.CreatedAt, pktLoc)
			if err != nil {
				createdAtTime, err = time.ParseInLocation("2006-01-02 15:04:05.000000", txn.CreatedAt, pktLoc)
				if err != nil {
					log.Println("Error parsing created_at:", err)
					continue
				}
			}

			// Parse updated_at - treating it as PKT time since DB stores in PKT
			updatedAtTime, err := time.ParseInLocation("2006-01-02 15:04:05", txn.UpdatedAt, pktLoc)
			if err != nil {
				updatedAtTime, err = time.ParseInLocation("2006-01-02 15:04:05.000000", txn.UpdatedAt, pktLoc)
				if err != nil {
					log.Println("Error parsing updated_at:", err)
					continue
				}
			}

			txnUnix := txnTime.Unix()
			createdAtUnix := createdAtTime.Unix()
			updatedAtUnix := updatedAtTime.Unix()

			// Handle deleted_at (can be null) - treating it as PKT time since DB stores in PKT
			var deletedAtUnix *int64
			if txn.DeletedAt != nil && *txn.DeletedAt != "" {
				deletedAtTime, err := time.ParseInLocation("2006-01-02 15:04:05", *txn.DeletedAt, pktLoc)
				if err != nil {
					deletedAtTime, err = time.ParseInLocation("2006-01-02 15:04:05.000000", *txn.DeletedAt, pktLoc)
					if err != nil {
						log.Println("Error parsing deleted_at:", err)
					} else {
						unix := deletedAtTime.Unix()
						deletedAtUnix = &unix
					}
				} else {
					unix := deletedAtTime.Unix()
					deletedAtUnix = &unix
				}
			}

			// Add to graph data with Unix timestamp
			graphData = append(graphData, resDTO.AggregatedGraphDataUnix{
				Key:    txnUnix,
				Amount: txn.Amount,
			})

			// Create Unix transaction object
			unixTxn := resDTO.GraphTransactionUnix{
				UserID:       txn.UserID,
				WalletID:     txn.WalletID,
				DrCr:         txn.DrCr,
				TxnDate:      txnUnix,
				TxnType:      txn.TxnType,
				ToFrom:       txn.ToFrom,
				Amount:       txn.Amount,
				Status:       txn.Status,
				CreatedAt:    createdAtUnix,
				UpdatedAt:    updatedAtUnix,
				DeletedAt:    deletedAtUnix,
				Purpose:      txn.Purpose,
				Icon:         txn.Icon,
				CardIcon:     txn.CardIcon,
				SenderWsID:   txn.SenderWsID,
				SenderName:   txn.SenderName,
				ReceiverWsID: txn.ReceiverWsID,
				ReceiverName: txn.ReceiverName,
				Brand:        txn.Brand,
				Last4:        txn.Last4,
			}

			allTransactions = append(allTransactions, unixTxn)
		}
	}

	return graphData, allTransactions, nil
}

// GetUserNotifications retrieves notifications for a user
func GetUserNotifications(userID uint, page int, limit int) ([]models.Notification, int64, error) {
	var notifications []models.Notification
	var total int64

	// Get total count
	if err := config.DB.Model(&models.Notification{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get paginated notifications
	offset := (page - 1) * limit
	if err := config.DB.Where("user_id = ?", userID).
		Order("created_at desc").
		Offset(offset).
		Limit(limit).
		Find(&notifications).Error; err != nil {
		return nil, 0, err
	}

	return notifications, total, nil
}

func FindUserByEmail(email string) (*models.User, error) {
	var user models.User
	err := config.DB.Where("email = ?", email).First(&user).Error
	return &user, err
}

func CreateUserByOAuth(user goth.User) (*models.User, error) {
	newUser := models.User{
		FirstName: user.FirstName,
		LastName:  user.LastName,
		Email:     user.Email,
		OAuthID:   &user.UserID,
		Provider:  "google",
	}

	_, err := createUser(config.DB, &newUser)
	if err != nil {
		return nil, err
	}

	return &newUser, nil
}
