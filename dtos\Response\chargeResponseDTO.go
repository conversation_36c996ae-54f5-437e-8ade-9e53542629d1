package ResponseDTO

type ChargeDTO struct {
	ID          uint    `json:"id"`
	Name        string  `json:"name"`
	Description string  `json:"description"`
	Amount      float64 `json:"amount"`
	Currency    string  `json:"currency"`
	IsActive    bool    `json:"is_active"`
	CreatedAt   string  `json:"created_at"`
	UpdatedAt   string  `json:"updated_at"`
}

type ChargesResponseDTO struct {
	APIResponse
	Data []ChargeDTO `json:"data"`
}

type ChargeResponseDTO struct {
	APIResponse
	Data ChargeDTO `json:"data"`
}
