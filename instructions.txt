How to run container:

sudo docker run -p 8081:8081 \
                -v /abs/path/to/dev.env/file:/root/dev.env # source:dest, dest should not change since binary is being launced from /root/ and main expects dev.env to be in "./"
                -v /abs/path/to/log/file/directory:/path/to/log/file/directory/in/container # make sure path to log file directory in dest is the same path as LOG_FILE in dev.env
                image_name