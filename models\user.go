package models

import (
	"time"
)

// type User struct {
// 	UserId         uint   `gorm:"primaryKey;autoIncrement"`
// 	Username       string `gorm:"size:100;not null;unique"`
// 	Email          string `gorm:"size:100;not null;unique"`
// 	HashedPassword string `gorm:"type:longtext"`
// 	//wsID
// 	CreatedAt time.Time `gorm:"autoCreateTime"`
// 	UpdatedAt time.Time `gorm:"autoUpdateTime"`
// }

type User struct {
	Id                uint      `gorm:"primaryKey;autoIncrement;" json:"id"`
	Email             string    `gorm:"size:100;not null;unique" json:"email"`
	FirstName         string    `gorm:"size:100;not null" json:"first_name"`
	LastName          string    `gorm:"size:100;not null" json:"last_name"`
	PhoneNumber       string    `gorm:"size:20;unique" json:"phone_number"`
	WSID              string    `gorm:"size:100;unique" json:"ws_id"` // Unique WABISABI ID
	Password          string    `gorm:"size:255" json:"password,omitempty"`
	PIN               string    `gorm:"size:255" json:"pin"` // Store hashed PIN
	CreatedAt         time.Time `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt         time.Time `gorm:"autoUpdateTime" json:"updated_at"`
	OAuthID           *string   `gorm:"size:255;unique" json:"oauth_id,omitempty"`       // Google User ID
	Provider          string    `gorm:"size:20;not null;default:'local" json:"provider"` // "local" or "google"
	IsVerify          bool      `gorm:"default:false" json:"is_verify"`
	StripeCustomerId  *string   `gorm:"size:255;unique" json:"stripe_customer_id"` // Stripe Customer ID
	OnfidoApplicantId *string   `gorm:"size:255;unique" json:"onfido_applicant_id"`
	PhoneCountryCode  string    `gorm:"size:10" json:"cc"`
	CountryCode       string    `gorm:"size:10" json:"cc_country"` // ISO 3166-1 alpha-2 code
}
