package ResponseDTO

import (
	"auth-service/models"
	"time"
)

type ChatDTO struct {
	SenderID          uint        `json:"senderid"`
	ReceiverID        uint        `json:"receiverid"`
	ChatType          string      `json:"chatType"`
	Chat              string      `json:"chat"`
	SenderImageLink   string      `json:"senderImageLink"`
	ReceiverImageLink string      `json:"receiverImageLink"`
	Data              interface{} `json:"data"`
}

type ChatConversationDTO struct {
	IsSuccess bool      `json:"Status"`
	Message   string    `json:"Message"`
	ChatData  []ChatDTO `json:"chatData"`
}

type MessageWithContent struct {
	models.Message
	Content string `json:"content"`
}

type ConversationResponseDTO struct{
	APIResponse
	Result []UserConversationDTO `json:"conversations"`
}

type UserConversationDTO struct {
	UserID          uint   `json:"user_id"`
	ConversationID  uint   `json:"conversation_id"`
	IsGroup         bool   `json:"is_group"`
	ConversationName string `json:"conversation_name"`
	LastMessageTime  string `json:"last_message_time"`
	LastMessage      string `json:"last_message"`
}

type FullMessage struct {
	ID          uint       `json:"id"`
	Content     *string    `json:"content"`
	MessageType string     `json:"message_type"`
	SentAt      time.Time  `json:"sent_at"`
	SenderID    uint       `json:"sender_id"`
	SenderName  string     `json:"sender_name"`
	MediaID     *uint      `json:"media_id,omitempty"`
	MediaType   *string    `json:"media_type,omitempty"`
	MediaURL    *string    `json:"media_url,omitempty"`
}

type ConversationDetailsDTO struct {
	APIResponse
	Result []FullMessage `json:"messages"`
}

type UnsentMessageDTO struct {
	SenderId 	uint	`json:"sender_id"`
	Message  	string  `json:"message"`
	Title       string  `json:"title"`
	MessageType string  `json:"message_type"`
	AgreementId uint    `json:"agreement_id"`
}