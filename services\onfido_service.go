package services

import (
	"auth-service/config"
	resDTO "auth-service/dtos/Response"
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"os"
	"time"

	"github.com/labstack/echo/v4"
)

type CreateWorkflowRunRequest struct {
	ApplicantID string `json:"applicant_id"`
	WorkflowID  string `json:"workflow_id"`
}

type WorkflowRunResponse struct {
	ID          string    `json:"id"`
	WorkflowID  string    `json:"workflow_id"`
	ApplicantID string    `json:"applicant_id"`
	Status      string    `json:"status"`
	SdkToken    string    `json:"sdk_token"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

type CreateApplicantRequest struct {
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Email     string `json:"email"`
}
type OnfidoWebhook struct {
	ResourceType string `json:"resource_type"`
	Action       string `json:"action"`
	Object       struct {
		ID          string `json:"id"`
		Status      string `json:"status"`
		WorkflowID  string `json:"workflow_id"`
		ApplicantID string `json:"applicant_id"`
		Output      struct {
			Result string `json:"result"` // approved or rejected
		} `json:"output"`
	} `json:"object"`
}

func CreateOnfidoApplicant(c echo.Context) error {
	var err error
	//var req CreateApplicantRequest
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		message := "Error converting user id"
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}
	user, err := getUserByID(config.DB, userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to get user by userID"})

	}
	if user.OnfidoApplicantId == nil {
		payload := CreateApplicantRequest{
			FirstName: user.FirstName,
			LastName:  user.LastName,
			Email:     user.Email,
		}

		jsonPayload, _ := json.Marshal(payload)

		client := &http.Client{}
		apiURL := os.Getenv("ONFIDO_API_URL") + "/applicants"

		request, _ := http.NewRequest("POST", apiURL, bytes.NewBuffer(jsonPayload))
		request.Header.Add("Authorization", "Token token="+os.Getenv("ONFIDO_API_TOKEN"))
		request.Header.Add("Content-Type", "application/json")

		res, err := client.Do(request)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to contact Onfido response"})
		}
		defer res.Body.Close()
		body, err := io.ReadAll(res.Body)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to read Onfido response"})
		}
		// Bind to model
		var applicant resDTO.OnfidoApplicant
		if err := json.Unmarshal(body, &applicant); err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to parse Onfido response"})
		}

		err = InsertOnfidoApplicantId(config.DB, user, applicant.ID, userID)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to insert Onfido applicant ID"})
		}
		user.OnfidoApplicantId = &applicant.ID
	}

	// Step 4: Optionally return the parsed response
	return c.JSON(http.StatusOK, map[string]string{"applicantID": *user.OnfidoApplicantId})
}
func CreateWorkflowRun(c echo.Context) error {
	//TODO: save workFlowId in staticConfiguration table
	var req CreateWorkflowRunRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			Message: "Invalid request body: " + err.Error(),
		})
	}

	payloadBytes, err := json.Marshal(req)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Failed to encode request payload",
		})
	}

	apiURL := os.Getenv("ONFIDO_API_URL") + "/workflow_runs"
	httpReq, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(payloadBytes))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Failed to create Onfido request",
		})
	}

	httpReq.Header.Set("Authorization", "Token token="+os.Getenv("ONFIDO_API_TOKEN"))
	httpReq.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	httpRes, err := client.Do(httpReq)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Failed to send request to Onfido",
		})
	}
	defer httpRes.Body.Close()

	resBody, err := io.ReadAll(httpRes.Body)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Failed to read Onfido response",
		})
	}

	if httpRes.StatusCode != http.StatusCreated {
		// Forward Onfido error response as JSONBlob
		return c.JSONBlob(httpRes.StatusCode, resBody)
	}

	var workflowResp WorkflowRunResponse
	if err := json.Unmarshal(resBody, &workflowResp); err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Failed to parse Onfido response",
		})
	}

	// ✅ Success — return parsed workflow response
	return c.JSON(http.StatusOK, workflowResp)
}
func OnfidoWebhookHandler(c echo.Context) error {
	var payload OnfidoWebhook
	if err := c.Bind(&payload); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			Message: "Invalid webhook payload",
		})
	}

	if payload.Action == "workflow_run.completed" {
		applicantID := payload.Object.ApplicantID
		result := payload.Object.Output.Result // approved or rejected

		// Example: update your DB based on result

		// Update in DB - pseudo code
		if result == "approved" {
			updates := map[string]interface{}{
				"is_verified": 1,
			}

			err := UpdateUserByParam(config.DB, "applicant_id", applicantID, updates)
			if err != nil {
				return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
					Message: "Failed to update user verification status",
				})
			}

		}

		return c.JSON(http.StatusOK, resDTO.APIResponse{
			Message: "Webhook processed successfully",
		})
	}

	// Ignore other webhook types
	return c.JSON(http.StatusOK, resDTO.APIResponse{
		Message: "Webhook ignored (not workflow_run.completed)",
	})
}
