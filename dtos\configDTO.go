package dtos

type Config struct {
	ENVIRONMENT        string `mapstructure:"ENVIRONMENT"`
	ROUTER_PATH        string `mapstructure:"ROUTER_PATH"`
	SERVER_ADDRESS     string `mapstructure:"SERVER_ADDRESS"`
	DB_HOST            string `mapstructure:"DB_HOST"`
	DB_PORT            string `mapstructure:"DB_PORT"`
	DB_USER            string `mapstructure:"DB_USER"`
	DB_PASSWORD        string `mapstructure:"DB_PASSWORD"`
	DB_NAME            string `mapstructure:"DB_NAME"`
	LOGS_API_SUBROUTER string `mapstructure:"LOGS_API_SUBROUTER"`
	JWT_SECRET_KEY     string `mapstructure:"JWT_SECRET_KEY"`
	PLAID_CLIENT_ID    string `mapstructure:"PLAID_CLIENT_ID"`
	PLAID_SECRET       string `mapstructure:"PLAID_SECRET"`
	CLIENT_ID          string `mapstructure:"CLIENT_ID"`
	CLIENT_SECRET      string `mapstructure:"CLIENT_SECRET"`
	CLIENT_CALLBACK    string `mapstructure:"C<PERSON>IENT_CALLBACK"`
	E<PERSON>_KEY            string `mapstructure:"ENC_KEY"`
}
