package config

import (
	"log"
	"os"

	"github.com/plaid/plaid-go/v14/plaid"
)

var PlaidClient *plaid.APIClient

func InitPlaid() {
	// Load from env vars or .env file
	clientID := os.Getenv("PLAID_CLIENT_ID")
	secret := os.Getenv("PLAID_SECRET")
	env := os.Getenv("PLAID_ENV") // "sandbox", "development", or "production"

	if clientID == "" || secret == "" || env == "" {
		log.Fatal("Missing Plaid configuration: make sure PLAID_CLIENT_ID, PLAID_SECRET, and PLAID_ENV are set.")
	}

	// Set environment
	var plaidEnv plaid.Environment
	switch env {
	case "sandbox":
		plaidEnv = plaid.Sandbox
	case "development":
		plaidEnv = plaid.Development
	case "production":
		plaidEnv = plaid.Production
	default:
		log.Fatal("Invalid PLAID_ENV value. Use sandbox, development, or production.")
	}

	// Configure Plaid client
	configuration := plaid.NewConfiguration()
	configuration.AddDefaultHeader("PLAID-CLIENT-ID", clientID)
	configuration.AddDefaultHeader("PLAID-SECRET", secret)
	configuration.UseEnvironment(plaidEnv)

	PlaidClient = plaid.NewAPIClient(configuration)
}
