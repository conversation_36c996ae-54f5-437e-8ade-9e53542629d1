package RequestDTO

type ConnectedAccountRequest struct {
	Email              string `json:"email"`
	AccountNumber      string `json:"account_number"`
	RoutingNumber      string `json:"routing_number"`
	Country            string `json:"country"`
	Currency           string `json:"currency"`
	AccountHolderName  string `json:"account_holder_name"`
	AccountHolderType  string `json:"account_holder_type"` // individual or company
	FirstName          string `json:"first_name"`
	LastName           string `json:"last_name"`
	DOBDay             int    `json:"dob_day"`
	DO<PERSON><PERSON>h           int    `json:"dob_month"`
	DOBYear            int    `json:"dob_year"`
	AddressLine1       string `json:"address_line1"`
	City               string `json:"city"`
	PostalCode         string `json:"postal_code"`
	ProductDescription string `json:"product_description,omitempty"`
}
