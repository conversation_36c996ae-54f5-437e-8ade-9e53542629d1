package controllers

import (
	"auth-service/services"

	"github.com/labstack/echo/v4"
)

func CreateStripeCustomer(c echo.Context) error {
	return services.CreateStripeCustomer(c)
}
func CreateConnectedAccount(c echo.Context) error {
	return services.CreateConnectedAccount(c)
}
func CreatePaymentMethods(c echo.Context) error {
	return services.CreateStripePaymentMethod(c)
}
func GetClientSecret(c echo.Context) error {
	return services.GetClientSecret(c)
}
func TopupStripe(c echo.Context) error {
	return services.CreateTopUpIntent(c)
}
func CreateKYCVerificationSession(c echo.Context) error {
	return services.CreateKYCVerificationSession(c)
}

func StripeWebhook(c echo.Context) error {
	return services.StripeWebhookHandler(c)
}

func GetKYCStatus(c echo.Context) error {
	return services.GetKYCStatus(c)
}

func GetUserSavedCards(c echo.Context) error {
	return services.GetUserSavedCards(c)
}
func AddCardToCustomer(c echo.Context) error {
	return services.AddCardToCustomer(c)
}
func SetDefaultCard(c echo.Context) error {
	return services.SetDefaultCard(c)
}
func DeleteCard(c echo.Context) error {
	return services.DeleteCard(c)
}
