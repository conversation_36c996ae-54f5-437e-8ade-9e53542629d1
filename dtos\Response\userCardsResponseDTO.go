package ResponseDTO

type CardInfo struct {
	Brand           string `json:"brand"`
	Last4           string `json:"last4"`
	ExpMonth        int64  `json:"exp_month"`
	ExpYear         int64  `json:"exp_year"`
	PaymentMethodID string `json:"payment_method_id"`
	IsDefault       bool   `json:"is_default"` // Optional, if needed
	CardIcon        string `json:"card_icon"`
	CardUserName    string `json:"card_user_name"` // Optional, if needed

}

type UserCardResponseDTO struct {
	APIResponse
	Cards []CardInfo `json:"cards"`
}
