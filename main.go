package main

import (
	"auth-service/config"

	"auth-service/middlewares"
	"auth-service/router"
	"os"

	"github.com/gorilla/sessions"
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
	"github.com/markbates/goth"
	"github.com/markbates/goth/gothic"
	"github.com/markbates/goth/providers/google"
)

var store = sessions.NewCookieStore([]byte("my_secret_key"))

func main() {

	// Initialize and connect to the database
	config.ConnectDB()
	// Initialize the Plaid client
	config.InitPlaid()
	config.InitStripe()
	e := echo.New()
	e.Binder = &middlewares.CustomBinder{}
	// Middleware for panic recover
	e.Use(middleware.Recover())
	e.Use(middleware.Logger())
	//e.Use(middlewares.ErrorLogger("ws-service"))
	gothic.Store = store

	// Configure Goth with Google OAuth
	clientID := os.Getenv("CLIENT_ID")
	clientSecret := os.Getenv("CLIENT_SECRET")
	client_Callback := os.Getenv("CLIENT_CALLBACK")
	goth.UseProviders(
		google.New(clientID, clientSecret, client_Callback, "email", "profile"),
	)
	router.RequestHandler(e)
}
