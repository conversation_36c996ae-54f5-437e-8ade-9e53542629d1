package services

import (
	"auth-service/config"
	reqDTO "auth-service/dtos/Request"
	resDTO "auth-service/dtos/Response"
	"fmt"
	"net/http"
	"strings"

	"github.com/labstack/echo/v4"
	"github.com/nyaruka/phonenumbers"
)

type NumberParts struct {
	CountryCode string
	CoreNumber  string
}

func PayContacts(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error converting user ID"})
	}

	var contactListRequestDTO reqDTO.ContactListRequestDTO
	if err := c.Bind(&contactListRequestDTO); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Invalid request"})
	}

	if len(contactListRequestDTO.UserContacts) == 0 {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "No contacts received"})
	}

	validContacts := make(map[string]NumberParts, 0)
	numberToOriginal := make(map[string][]string, 0)
	
	for _, rawNumber := range contactListRequestDTO.UserContacts {
		var countryCode, coreNumber string
		isValid := false
		
		// Try parsing as international E164 format first
		num, err := phonenumbers.Parse(rawNumber, "")
		if err == nil && phonenumbers.IsValidNumber(num) {
			countryCode = fmt.Sprintf("+%d", num.GetCountryCode())
			coreNumber = fmt.Sprintf("%d", num.GetNationalNumber())
			isValid = true
		}
		
		// If invalid on international then try parsing as Spanish local number
		if !isValid {
			num, err = phonenumbers.Parse(rawNumber, "ES")
			if err == nil && phonenumbers.IsValidNumber(num) {
				region := phonenumbers.GetRegionCodeForNumber(num)
				if region == "ES" {
					countryCode = fmt.Sprintf("+%d", num.GetCountryCode())
					coreNumber = fmt.Sprintf("%d", num.GetNationalNumber())
					isValid = true
				}
			}
		}

		// If still invalid on international then try parsing as Pakistani local number
		if !isValid {
			num, err = phonenumbers.Parse(rawNumber, "PK")
			if err == nil && phonenumbers.IsValidNumber(num) {
				region := phonenumbers.GetRegionCodeForNumber(num)
				if region == "PK" {
					countryCode = fmt.Sprintf("+%d", num.GetCountryCode())
					coreNumber = fmt.Sprintf("%d", num.GetNationalNumber())
					isValid = true
				}
			}
		}
		
		if isValid {
			numberKey := coreNumber + countryCode
			numberToOriginal[numberKey] = append(numberToOriginal[numberKey], rawNumber)
			validContacts[rawNumber] = NumberParts{
				CoreNumber:  coreNumber,
				CountryCode: countryCode,
			}
		}

		// If neither parsing works, we ignore this number
	}
	
	// Deduplicate and prioritize international format
	finalContacts := make(map[string]NumberParts, 0)
	for _, originalFormats := range numberToOriginal {

		// Find the best format to use (prioritize international)
		bestFormat := originalFormats[0]
		for _, format := range originalFormats {
			if strings.HasPrefix(format, "+") {
				bestFormat = format
				break
			}
		}
		
		// Use the best format as the key
		finalContacts[bestFormat] = validContacts[bestFormat]
	}
	
	validContacts = finalContacts

	if len(validContacts) == 0 {
		return c.JSON(http.StatusOK, resDTO.ContactResponseDTO{
			APIResponse: resDTO.APIResponse{
				IsSuccess: true,
				Message:   "No valid numbers found",
			},
			Contacts: []resDTO.Contact{},
		})
	}

	filteredContacts, err := GetUserContacts(config.DB, validContacts, userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Database error"})
	}

	return c.JSON(http.StatusOK, resDTO.ContactResponseDTO{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "Successfully fetched registered contacts",
		},
		Contacts: filteredContacts,
	})
}