package models

import (
	"time"
)

type Charge struct {
	ID          uint    `gorm:"primaryKey"`
	Name        string  `gorm:"size:100;not null"` // e.g., "wallet_transfer", "card_topup"
	Description string  `gorm:"size:255"`
	Amount      float64 `gorm:"not null"`
	Currency    string  `gorm:"size:3;default:'USD'"`
	IsActive    bool    `gorm:"default:true"`
	CreatedAt   time.Time
	UpdatedAt   time.Time
}
