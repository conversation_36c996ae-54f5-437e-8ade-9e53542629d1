package middlewares

import (
	"auth-service/services"
	"bytes"
	"net/http"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
)

// ResponseCapture wraps the http.ResponseWriter to capture response body
type ResponseCapture struct {
	http.ResponseWriter
	Body *bytes.Buffer
}

func (r *ResponseCapture) Write(b []byte) (int, error) {
	r.Body.Write(b)                  // capture into buffer
	return r.ResponseWriter.Write(b) // write to actual client
}

// ErrorLogger middleware logs errors and captures response
func ErrorLogger(serviceName string) echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {
			// If this is a WebSocket upgrade, skip wrapping response writer
			if c.Request().Header.Get("Upgrade") == "websocket" {
				return next(c)
			}
			// Generate a unique request ID
			requestID := uuid.New().String()
			c.Set("request_id", requestID)

			// Get user ID if available
			var userID *uint
			if id, ok := c.Get("user_id").(uint); ok {
				userID = &id
			}

			// Replace the response writer
			res := c.Response()
			originalWriter := res.Writer
			respCapture := &ResponseCapture{
				ResponseWriter: originalWriter,
				Body:           &bytes.Buffer{},
			}
			res.Writer = respCapture

			// Call the next handler
			err := next(c)

			// Capture the response body
			responseBody := respCapture.Body.String()
			print(responseBody)
			// Log only if error or non-200 status
			if err != nil || res.Status != http.StatusOK {
				if err == nil {
					err = echo.NewHTTPError(res.Status, "Request failed :"+responseBody)
				}

				// Optional: log the response body
				services.LogError(err, serviceName, userID, requestID)
			}

			return err
		}
	}
}
