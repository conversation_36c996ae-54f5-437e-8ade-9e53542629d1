package services

import (
	"sync"

	"github.com/gorilla/websocket"
)

type activeConn struct {
	user uint
	conn *websocket.Conn
}

type ConnectionHub struct {
	bg_connections     map[uint]*websocket.Conn
	active_connections map[uint]activeConn
	bg_mutex           sync.RWMutex
	active_mutex       sync.RWMutex
}

// NewActiveHub creates a new hub for active chat connections
func NewActiveHub() *ConnectionHub {
	return &ConnectionHub{
		active_connections: make(map[uint]activeConn),
	}
}

// NewBackgroundHub creates a new hub for background connections
func NewBackgroundHub() *ConnectionHub {
	return &ConnectionHub{
		bg_connections: make(map[uint]*websocket.Conn),
	}
}

func (h *ConnectionHub) RegisterActiveUser(senderId uint, receiverId uint, connection *websocket.Conn) {
	h.active_mutex.Lock()
	defer h.active_mutex.Unlock()

	h.active_connections[senderId] = activeConn{
		user: receiverId,
		conn: connection,
	}
}

func (h *ConnectionHub) UnregisterActiveUser(senderId uint) {
	h.active_mutex.Lock()
	defer h.active_mutex.Unlock()
	delete(h.active_connections, senderId)
}

func (h *ConnectionHub) RegisterBackgroundUser(userID uint, conn *websocket.Conn) {
	h.bg_mutex.Lock()
	defer h.bg_mutex.Unlock()
	h.bg_connections[userID] = conn
}

func (h *ConnectionHub) UnregisterBackgroundUser(userID uint) {
	h.bg_mutex.Lock()
	defer h.bg_mutex.Unlock()
	delete(h.bg_connections, userID)
}

// GetConn retrieves a connection for a user if it exists
func (h *ConnectionHub) GetBackroundConnection(userID uint) (*websocket.Conn, bool) {
	h.bg_mutex.RLock()
	defer h.bg_mutex.RUnlock()
	conn, exists := h.bg_connections[userID]
	return conn, exists
}

func (h *ConnectionHub) GetActiveConnection(userID uint) (*activeConn, bool) {
	h.active_mutex.RLock()
	defer h.active_mutex.RUnlock()
	activeConn, exists := h.active_connections[userID]
	return &activeConn, exists
}
