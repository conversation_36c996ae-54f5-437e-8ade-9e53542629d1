package services

import (
	"auth-service/config"
	resDto "auth-service/dtos/Response"
	"auth-service/models"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"log"
	"net/http"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/ephemeralkey"
	"github.com/stripe/stripe-go/v72/identity/verificationsession"
	"github.com/stripe/stripe-go/v72/webhook"
	"gorm.io/gorm"
)

func CreateKYCVerificationSession(c echo.Context) error {
	// Step 1: Get user ID from context
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Invalid user ID"})
	}

	//  Get verification method from query params
	verificationMethod := c.QueryParam("isManual")
	// if verificationMethod != "manual" && verificationMethod != "stripe" {
	// 	return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Invalid verification method. Must be 'manual' or 'stripe'"})
	// }
	// Step 2: Fetch user from DB
	user, err := getUserByID(config.DB, userID)
	if err != nil || user.Id == 0 {
		return c.JSON(http.StatusNotFound, resDto.APIResponse{Message: "User not found"})
	}

	// Step 3: Check if KYC session already exists
	var kyc models.KYCVerification
	result := config.DB.Where("user_id = ?", userID).First(&kyc)

	// Check if check for manual verification is true
	// if kyc.FlaggedForManualReview {
	// 	return c.JSON(http.StatusOK, resDto.APIResponse{Message: "User flagged for manual review"})
	// }
	if strings.ToLower(verificationMethod) == "true" {
		// Generate UUID for verification session
		sessionID := uuid.New().String()

		if result.Error == gorm.ErrRecordNotFound {
			// Create new KYC verification record
			kyc = models.KYCVerification{
				UserID:                 userID,
				VerificationSessionID:  sessionID,
				Status:                 "pending",
				KYCAttempts:            1,
				FlaggedForManualReview: true,
			}
			if err := config.DB.Create(&kyc).Error; err != nil {
				return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to create manual KYC session"})
			}
			return c.JSON(http.StatusInternalServerError, resDto.APIResponse{IsSuccess: true, Message: "Manual KYC verification session is created"})
		} else {
			// Update existing KYC verification record
			updates := map[string]interface{}{
				"verification_session_id":   sessionID,
				"status":                    "pending",
				"verified_at":               nil,
				"last_error_code":           "",
				"document_type":             "",
				"kyc_attempts":              gorm.Expr("kyc_attempts + 1"),
				"flagged_for_manual_review": true,
				"updated_at":                time.Now(),
			}
			if err := config.DB.Model(&models.KYCVerification{}).
				Where("user_id = ?", userID).
				Updates(updates).Error; err != nil {
				return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to update manual KYC session"})
			}
		}
		return c.JSON(http.StatusOK, resDto.APIResponse{IsSuccess: true, Message: "Manual KYC verification session is already created"})

		// return c.JSON(http.StatusOK, resDto.StripeVerificationResponse{
		//     APIResponse:  resDto.APIResponse{IsSuccess: true, Message: "Manual verification session created"},
		//     ClientSecret: sessionID,
		//     SessionID:    sessionID,
		// })
	}
	// Step 4: If session exists, try to reuse it
	if result.Error == nil && result.Error != gorm.ErrRecordNotFound {
		//if result.Error == nil && kyc.VerificationSessionID != ""  {
		session, err := verificationsession.Get(kyc.VerificationSessionID, nil)
		if err == nil && session.Status != stripe.IdentityVerificationSessionStatusCanceled &&
			session.Status != stripe.IdentityVerificationSessionStatusVerified {
			ek_params := &stripe.EphemeralKeyParams{
				StripeVersion: stripe.String("2025-04-30.basil"),
			}
			ek_params.AddExtra("verification_session", session.ID)
			ek, _ := ephemeralkey.New(ek_params)
			// If session is still usable, return it
			return c.JSON(http.StatusOK, resDto.StripeVerificationResponse{
				APIResponse:  resDto.APIResponse{IsSuccess: true, Message: "Reusing existing session"},
				ClientSecret: session.ClientSecret,
				SessionID:    session.ID,
				EmpheralKey:  ek.Secret,
			})
		}
		if session.Status == stripe.IdentityVerificationSessionStatusVerified {
			return c.JSON(http.StatusOK, resDto.APIResponse{IsSuccess: true, Message: "User already verified"})
		}

	}

	// Step 5: Create new Verification Session
	params := &stripe.IdentityVerificationSessionParams{
		Type: stripe.String("document"),
		Options: &stripe.IdentityVerificationSessionOptionsParams{
			Document: &stripe.IdentityVerificationSessionOptionsDocumentParams{
				AllowedTypes: stripe.StringSlice([]string{"driving_license", "passport", "id_card"}),
				//RequireIDNumber:       stripe.Bool(true),
				RequireLiveCapture:    stripe.Bool(true),
				RequireMatchingSelfie: stripe.Bool(true),
			},
		},
	}
	params.AddMetadata("user_id", fmt.Sprint(userID))
	params.AddMetadata("first_name", user.FirstName)
	params.AddMetadata("last_name", user.LastName)

	session, err := verificationsession.New(params)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: err.Error()})
	}
	// Create an ephemeral key for the VerificationSession
	ek_params := &stripe.EphemeralKeyParams{
		StripeVersion: stripe.String("2025-04-30.basil"),
	}
	ek_params.AddExtra("verification_session", session.ID)
	ek, _ := ephemeralkey.New(ek_params)
	// Step 6: Save or update session in DB
	if result.Error == gorm.ErrRecordNotFound {
		kyc = models.KYCVerification{
			UserID:                userID,
			VerificationSessionID: session.ID,
			Status:                "pending",
			KYCAttempts:           1,
		}
		if err := config.DB.Create(&kyc).Error; err != nil {
			return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to save KYC session"})
		}
	} else {
		update := map[string]interface{}{
			"verification_session_id": session.ID,
			"status":                  "pending",
			"verified_at":             nil,
			"last_error_code":         "",
			"document_type":           "",
			"kyc_attempts":            gorm.Expr("kyc_attempts + 1"),
			"updated_at":              time.Now(),
		}
		if err := config.DB.Model(&models.KYCVerification{}).
			Where("user_id = ?", userID).
			Updates(update).Error; err != nil {
			return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Failed to update KYC session"})
		}
	}

	// Step 7: Return client secret
	return c.JSON(http.StatusOK, resDto.StripeVerificationResponse{
		APIResponse:  resDto.APIResponse{IsSuccess: true, Message: "Verification session created"},
		ClientSecret: session.ClientSecret,
		SessionID:    session.ID,
		EmpheralKey:  ek.Secret,
	})
}

func StripeWebhookHandler(c echo.Context) error {
	// Try to make pk = userId+id
	stripeSecret := os.Getenv("STRIPE_WEBHOOK_SECRET")

	body, err := ioutil.ReadAll(c.Request().Body)
	if err != nil {
		log.Printf("Read error: %v", err)
		return c.String(http.StatusBadRequest, "Failed to read request body")
	}

	event, err := webhook.ConstructEvent(body, c.Request().Header.Get("Stripe-Signature"), stripeSecret)
	if err != nil {
		log.Printf("Webhook signature verification failed: %v", err)
		return c.String(http.StatusBadRequest, "Signature verification failed")
	}

	switch event.Type {
	case "identity.verification_session.verified":
		var session stripe.IdentityVerificationSession
		if err := json.Unmarshal(event.Data.Raw, &session); err != nil {
			log.Printf("Unmarshal error: %v", err)
			return c.String(http.StatusBadRequest, "Bad JSON")
		}

		updates := map[string]interface{}{
			"status":              session.Status,
			"selfie_check_passed": 1,
		}
		if err := UpdateKYCVerificationStatus(config.DB, session.ID, updates); err != nil {
			log.Printf("Failed to update verification status: %v", err)
		}

	case "identity.verification_session.requires_input":
		var session stripe.IdentityVerificationSession
		if err := json.Unmarshal(event.Data.Raw, &session); err != nil {
			log.Printf("Unmarshal error: %v", err)
			return c.String(http.StatusBadRequest, "Bad JSON")
		}

		userIDStr := session.Metadata["user_id"]
		log.Printf("Verification requires input for user: %s", userIDStr)

		var kyc models.KYCVerification
		if err := config.DB.Where("verification_session_id = ?", session.ID).First(&kyc).Error; err != nil {
			log.Printf("Session not found: %v", err)
			break
		}

		updates := map[string]interface{}{
			"status":       string(session.Status),
			"kyc_attempts": gorm.Expr("kyc_attempts + 1"),
		}

		if session.LastError != nil {
			updates["last_error_code"] = string(session.LastError.Code)
		}

		if session.LastVerificationReport != nil && session.LastVerificationReport.Selfie != nil {
			updates["selfie_error_reason"] = session.LastVerificationReport.Selfie.Error.Reason
		}

		// if kyc.KYCAttempts >= 3 && session.LastVerificationReport != nil {
		// 	updates["flagged_for_manual_review"] = true
		// }

		if err := UpdateKYCVerificationStatus(config.DB, session.ID, updates); err != nil {
			log.Printf("Failed to update KYC verification: %v", err)
		}
		userIDUint64, err := strconv.ParseUint(userIDStr, 10, 32)
		if err != nil {
			// handle error
			fmt.Println("Invalid userID:", err)
		}
		userID := uint(userIDUint64)
		BroadcastNotification(userID, "Kyc Verification",
			"KYC verification Result",
			fmt.Sprintf("KYC verification Result: %v", session.Status),
			map[string]interface{}{
				"KYC verification Result": session.Status,
			})
	default:
		log.Printf("Unhandled event type: %s", event.Type)
	}

	return c.JSON(http.StatusOK, map[string]string{"status": "received"})
}
func GetKYCStatus(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: "Invalid user ID"})
	}

	var userKycStatus models.KYCVerification
	result := config.DB.Where("user_id = ?", userID).First(&userKycStatus)
	if result.Error != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Database error. Failed to get KYC status"})
	}

	return c.JSON(http.StatusOK, resDto.KYCStatusResponseDTO{
		APIResponse: resDto.APIResponse{
			IsSuccess: true,
			Message:   "KYC status retrieved",
		},
		KycStatus: userKycStatus.Status,
	})
}

//TODO: Compare the name in ID and UserDetails
//Make function to compare the name And if not match update the kyc result manually or add coulumn manually not verified

// Example of how to get the name from the verification report ID
// session, err := verificationsession.Get("vs_...", nil)
// reportID := session.LastVerificationReport
// nameFromID := reportID.Document.FirstName + " " + reportID.Document.LastName
// print(nameFromID)
