package ResponseDTO

type BeneficiaryWalletMapDTO struct {
	BeneficiaryId     uint   `json:"beneficiary_id"`
	BeneficiaryName   string `json:"beneficiary_name"`
	WSID              string `json:"ws_id"`
	BeneficiaryType   string `json:"beneficiary_type"`
	BeneficiaryTypeId uint   `json:"beneficiary_type_id"`
	IsFav             bool   `json:"is_fav"`
	CreatedAt         string `json:"created_at"`
	LastPayment       int    `json:"last_payment"`
	LastPaymentDate   int    `json:"last_payment_date"`
	//WalletNumber    	string `json:"wallet_number"`
}

type BeneficiaryWalletResponseDTO struct {
	APIResponse
	BeneficiaryWalletMapDTO []BeneficiaryWalletMapDTO `json:"beneficiaries"`
}
