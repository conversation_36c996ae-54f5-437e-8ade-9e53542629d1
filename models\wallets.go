package models

import (
	"time"

	"gorm.io/gorm"
)

type Wallet struct {
	ID        string    `gorm:"size:100;primaryKey"`
	UserID    uint      `gorm:"not null;unique"`
	Amount    string    `gorm:"not null;size:1024"`
	Currency  string    `gorm:"size:3;not null"`
	CreatedAt time.Time `gorm:"autoCreateTime"`
	UpdatedAt time.Time `gorm:"autoUpdateTime"`
	gorm.Model
	User User `gorm:"foreignKey:UserID;references:id"`
}