package ResponseDTO

type LoginResponse struct {
	APIResponse
	UserData    UserData
	UserDetails UserDetailsDTO
	Card        *CardInfo
}

type UserKYCInfo struct {
	IsVerify        bool   `gorm:"column:is_verify"`
	Status          string `gorm:"column:status"`
	PaymentMethodID string `gorm:"column:method_token"`
}

type UserData struct {
	FullName     string `json:"full_name"`
	Token        string `json:"token"`
	IsUserDetail bool   `json:"is_user_detail,omitempty"`
	//IsKyc            bool   `json:"is_kyc"`
	IsKyc            string `json:"is_kyc"`
	PhoneNumber      string `json:"phone_number"`
	WS_id            string `json:"ws_id"`
	CountryCode      string `json:"cc"`
	PhoneCountryCode string `json:"cc_country_code"`
	PaymentMethodID  string `json:"payment_method_id"`
}
