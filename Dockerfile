# Step 1: Build the Go app
FROM golang:1.23-alpine AS builder

# Alpine doesn't contain Time Zone database by default
RUN apk add --no-cache tzdata

# Set the working directory inside the container
WORKDIR /app

# Copy go.mod and go.sum files for dependency installation
COPY go.mod go.sum ./

# Download and cache Go modules
RUN go mod download

# Copy the rest of the application code
COPY . .

# Build the Go app for the production environment
RUN go build -o main .

# Step 2: Use a smaller image to run the Go app
FROM alpine:latest
RUN apk add --no-cache tzdata

# Set the working directory inside the container
WORKDIR /root/

# Copy environment file
COPY prod.env /root/prod.env

# Copy the built Go app from the builder image
COPY --from=builder /app/main .

# Expose the application port (default: 8081)
EXPOSE 8081
ENV ACTIVE_PROFILE=PRODUCTION
# Load environment variables and start the app
CMD ["sh", "-c", "export $(grep -v '^#' /root/prod.env | xargs) && ./main"]
