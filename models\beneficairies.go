package models

import (
	"time"
)

type Beneficiaries struct {
	ID                uint      `gorm:"primaryKey;autoIncrement"`
	UserID            uint      `gorm:"not null;constraint:OnDelete:CASCADE;foreignKey:UserID;references:ID"`        // The user saving the beneficiary
	BeneficiaryID     uint      `gorm:"not null;constraint:OnDelete:CASCADE;foreignKey:BeneficiaryID;references:ID"` // The user being saved
	BeneficiaryTypeID uint      // FK to BeneficiaryType
	Name              string    `gorm:"size:100"`       // Optional: Custom name
	AccountNumber     *string   `gorm:"size:34;unique"` // Optional: External bank account
	BankName          *string   `gorm:"size:255"`       // Optional: Bank name if IBAN is present
	CreatedAt         time.Time `gorm:"autoCreateTime"`
	UpdatedAt         time.Time `gorm:"autoUpdateTime"`
	IsFav             bool      `json:"is_fav"`
	// Foreign Keys
	User            User            `gorm:"foreignKey:UserID;references:Id"`
	BeneficiaryType BeneficiaryType `gorm:"foreignKey:BeneficiaryTypeID;references:Id;constraint:OnDelete:CASCADE"`

	// You can also add this if you want a relation to the actual beneficiary user:
	// BeneficiaryUser   User           `gorm:"foreignKey:BeneficiaryID;references:ID"`
}
