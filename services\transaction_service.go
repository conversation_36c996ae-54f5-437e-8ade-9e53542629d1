package services

import (
	"auth-service/config"
	reqDTO "auth-service/dtos/Request"
	resDTO "auth-service/dtos/Response"
	"net/http"
	"strconv"
	"time"

	"github.com/labstack/echo/v4"
)

func CheckWSID(wsid string) bool {

	checkuser, err := getUserByWSID(config.DB, wsid)
	if err != nil {
		return false
	}

	if checkuser.WSID != "" {
		return true
	} else {
		return false
	}

}

// flatten response + name + time
func TransferFundsWallet(c echo.Context) error {
	charge, err := GetChargeByType("wallet_transfer")
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Error getting transfer charge",
		})
	}

	fromUserId, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error converting user ID"})
	}

	var transferRequest reqDTO.TransferRequest
	transferRequest.FromUserID = fromUserId

	if err := c.Bind(&transferRequest); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Invalid request"})
	}

	user, err := getUserByID(config.DB, fromUserId)
	if err != nil {
		return err
	}

	if !CheckPasswordHash(transferRequest.Pin, user.PIN) {
		return c.JSON(http.StatusUnauthorized, resDTO.APIResponse{Message: "Invalid PIN"})
	}

	wsIdExists := CheckWSID(transferRequest.WabisabiID)

	if !wsIdExists {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "WSID doesn't exist, can't transfer"})
	}

	if transferRequest.Amount <= float64(0) {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Cannot transfer less than or 0 amount"})
	}
	totalAmount := transferRequest.Amount + charge.Amount
	transferRequest.Amount = totalAmount

	// process transaction
	statusCode, err, dbRes := InitiateWallettoWalletTransactionDB(config.DB, transferRequest, user.Email)
	if err != nil {
		return c.JSON(statusCode, resDTO.APIResponse{Message: err.Error()})
	}

	// receiverUser, err := getUserByWsId(config.DB, transferRequest.WabisabiID)
	// if err != nil {
	// 	return err
	// }

	return c.JSON(http.StatusOK, resDTO.TransactionResponse{
		APIResponse:  resDTO.APIResponse{IsSuccess: true, Message: "Transfer successful"},
		Amount:       transferRequest.Amount,
		BankName:     "ABC_BANK",
		ReceiverName: dbRes.ReceiverName,
		TxnTime:      dbRes.TxnTime,
	})

}

func ValidatePinCode(c echo.Context) error {
	type pin struct {
		PinCode string `json:"pin_code"`
	}

	var req pin
	fromUserId, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		message := "Error converting user id"
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Invalid request"})
	}

	user, err := getUserByID(config.DB, fromUserId)
	if err != nil {
		return err
	}

	// checking pin
	if !CheckPasswordHash(req.PinCode, user.PIN) {

		message := "Invalid PIN"
		response := resDTO.APIResponse{Message: message}

		return c.JSON(http.StatusUnauthorized, response)
	}

	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: "Pin Verified"})
}

func TransferHistory(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error converting user ID"})
	}

	fromUnix := c.QueryParam("from")
	toUnix := c.QueryParam("to")

	if fromUnix == "" || toUnix == "" {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Both 'from' and 'to' unix timestamps must be provided"})
	}

	fromTimestamp, err := strconv.ParseInt(fromUnix, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Invalid 'from' unix timestamp"})
	}

	toTimestamp, err := strconv.ParseInt(toUnix, 10, 64)
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Invalid 'to' unix timestamp"})
	}

	// Set PKT timezone
	pktLoc, err := time.LoadLocation("Asia/Karachi")
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to load PKT timezone"})
	}

	fromDate := time.Unix(fromTimestamp, 0).In(pktLoc).Format("2006-01-02")
	toDate := time.Unix(toTimestamp, 0).In(pktLoc).Format("2006-01-02")

	var result []resDTO.DateTransactionGroup
	result, err = TransactionHistoryDetails(userID, fromDate, toDate)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Failed to fetch transaction history"})
	}

	return c.JSON(http.StatusOK, resDTO.TransactionHistoryResponseDTO{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "Successfully fetched transaction history",
		},
		TransactionHistory: result,
	})
}

func GetPaymentTypes(c echo.Context) error {
	c.Set("serviceName", "payment_method_service")

	paymentMethods, err := GetPaymentTypesFromDB(config.DB)
	if err != nil {
		c.Set("customMessage", "Failed to fetch Purpose")
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Failed to fetch Purpose",
		})
	}

	// Convert to DTO
	purposeDTOs := make([]resDTO.PaymentTypeDTO, len(paymentMethods))
	for i, pm := range paymentMethods {
		//iconBase64 := base64.StdEncoding.EncodeToString(pm.Icon)

		purposeDTOs[i] = resDTO.PaymentTypeDTO{
			ID:      pm.Id,
			Purpose: pm.Purpose,
			Icon:    pm.Icon,
		}
	}

	c.Set("customMessage", "Payment methods fetched successfully")
	return c.JSON(http.StatusOK, resDTO.PaymentMethodResponse{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "Purpose fetched successfully",
		},
		Purpose: purposeDTOs,
	})
}
