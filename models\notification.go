package models

import (
	"time"

	"gorm.io/gorm"
)

type Notification struct {
	ID        uint           `gorm:"primarykey" json:"id"`
	UserID    uint           `json:"user_id"`
	Type      string         `json:"type"` // payment_received, topup, wallet_transfer, beneficiary_added, kyc_verified
	Title     string         `json:"title"`
	Message   string         `json:"message"`
	Data      string         `json:"data"` // Store as JSON string
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`
	IsRead    bool           `gorm:"default:false" json:"is_read"`
}
