version: '3.8'

name: ws_app

services:
  api:
    build: .
    container_name: wallet-api
    ports:
      - "8081:8081"
    environment:
      - ACTIVE_PROFILE=PRODUCTION
    volumes:
      - .:/app


#   db:
#     image: mysql:latest
#     restart: always
#     ports:
#       - "3306:3306"
#     networks:
#       - ws_net
#     environment:
#       MYSQL_ROOT_PASSWORD: Tafsol786?
#       MYSQL_DATABASE: wspay
#     healthcheck:
#       test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
#       interval: 10s
#       timeout: 5s
#       retries: 3

# networks:
#   ws_net:
#     driver: bridge
