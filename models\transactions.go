package models

import (
	"time"

	"gorm.io/gorm"
)

type Transaction struct {
	ID            uint        `gorm:"primaryKey;autoincrement"`
	UserID        uint        `gorm:"not null;constraint:OnDelete:CASCADE;foreignKey:UserID;references:ID"`
	WalletID      string      `gorm:"size:100;not null;constraint:OnDelete:CASCADE;foreignKey:WalletID;references:ID"`
	DrCr          string      `gorm:"size:10;not null"` // "DR" for debit, "CR" for credit
	TxnDate       time.Time   `gorm:"autoCreateTime"`
	TxnType       string      `gorm:"size:50;not null"`
	ToFrom        string      `gorm:"size:100;not null"`
	Amount        float64     `gorm:"not null"`
	Status        string      `gorm:"size:20;not null"`
	PaymentTypeId uint        `gorm:"constraint:OnDelete:CASCADE;foreignKey:PaymentTypeId;references:id"`
	PaymentType   PaymentType `gorm:"foreignKey:PaymentTypeId;references:id"`
	User          User        `gorm:"foreignKey:UserID;references:id"`
	Wallet        Wallet      `gorm:"foreignKey:WalletID;references:id"`
	gorm.Model
}
