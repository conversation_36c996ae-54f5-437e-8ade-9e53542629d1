package ResponseDTO

import "time"

type MessageDTO struct {
	ID          uint      `json:"id"`
	SenderID    uint      `json:"sender_id"`
	ReceiverID  uint      `json:"receiver_id"`
	Status      string    `json:"status"`
	MessageType string    `json:"message_type"`
	CreatedAt   time.Time `json:"created_at"`

	// For text messages
	Content string `json:"content,omitempty"`

	// For agreement messages (simplified, fetch details separately)
	AgreementID    uint   `json:"agreement_id,omitempty"`
	AgreementTitle string `json:"agreement_title,omitempty"`
}