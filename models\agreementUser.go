package models

import "time"

type AgreementUser struct {
	ID          uint      `gorm:"primaryKey;autoIncrement" json:"id"`
	AgreementID uint      `gorm:"not null;index" json:"agreement_id"`
	UserID      uint      `gorm:"not null;index" json:"user_id"`
	Role        string    `gorm:"not null;type:enum('creator','participant','observer')" json:"role"`
	Status      string    `gorm:"not null;type:enum('invited','accepted','declined')" json:"status"`
	JoinedAt    time.Time `gorm:"null" json:"joined_at"`
	Agreement   Agreement `gorm:"foreignKey:AgreementID;references:MessageID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"-"`
	User        User      `gorm:"foreignKey:UserID;references:id"`
}
