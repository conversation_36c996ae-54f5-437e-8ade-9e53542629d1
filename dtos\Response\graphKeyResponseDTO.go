package ResponseDTO

import "auth-service/models"

type GraphKeyResponseDTO struct {
	APIResponse
	GraphKeys []models.GraphKeys `json:"graph_keys"`
}

type GraphTransactionResponseDTO struct {
	APIResponse
	Transactions []GraphTransactionNew `json:"transactions"`
}

type GraphDateTransactionGroup struct {
	Date        string                `json:"date"`         // Unix timestamp for date grouping
	TransDetail []GraphTransactionNew `json:"trans_detail"` // Transactions with string timestamps
}

type GraphTransactionHistoryResponseDTO struct {
	APIResponse
	TransactionHistory []GraphDateTransactionGroup `json:"transaction_history"`
}

type GraphTransaction7DaysResponseDTO struct {
	APIResponse
	GraphData          []AggregatedGraphData       `json:"graph_data"`
	TransactionHistory []GraphTransactionNew      `json:"transaction_history"`
}

// Structure for aggregated graph data (daily totals)
type AggregatedGraphData struct {
	Key   string  `json:"key"`   // Format: "2006-01-02"
	Amount float64 `json:"amount"` // Total amount for the day
}