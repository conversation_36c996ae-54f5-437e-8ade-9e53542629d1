package services

import (
	cfg "auth-service/config"
	"log"

	"auth-service/models"
	"bytes"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"errors"
	"fmt"
	"io"
	"mime"
	"net/http"
	"os"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"time"

	"golang.org/x/crypto/hkdf"
	"gorm.io/gorm"

	"cloud.google.com/go/storage"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/go-sql-driver/mysql"
	"github.com/stripe/stripe-go/v72"
	"github.com/stripe/stripe-go/v72/file"
)

// InterfaceToUint converts an interface{} to uint safely
func InterfaceToUint(value interface{}) (uint, error) {
	switch v := value.(type) {
	case int:
		return uint(v), nil
	case int8, int16, int32, int64:
		return uint(v.(int64)), nil
	case uint, uint8, uint16, uint32, uint64:
		return v.(uint), nil
	case float32, float64:
		return uint(v.(float64)), nil
	case string:
		parsed, err := strconv.ParseUint(v, 10, 64)
		if err != nil {
			return 0, fmt.Errorf("invalid string format: %v", err)
		}
		return uint(parsed), nil
	default:
		return 0, fmt.Errorf("unsupported type: %T", v)
	}
}

func ValidatePassword(password string) error {
	// Minimum 8 characters
	if len(password) < 8 {
		return fmt.Errorf("password must be at least 8 characters long")
	}

	// Contains at least one uppercase letter
	upperCase, _ := regexp.MatchString(`[A-Z]`, password)
	if !upperCase {
		return fmt.Errorf("password must contain at least one uppercase letter")
	}

	// Contains at least one lowercase letter
	lowerCase, _ := regexp.MatchString(`[a-z]`, password)
	if !lowerCase {
		return fmt.Errorf("password must contain at least one lowercase letter")
	}

	// Contains at least one special character
	specialChar, _ := regexp.MatchString(`[!@#\$%\^&\*()_+={}\[\]:;"'<>,.?/~]`, password)
	if !specialChar {
		return fmt.Errorf("password must contain at least one special character")
	}

	return nil // Password is valid
}

// EncryptAES encrypts plaintext using AES CBC with IV embedded in the ciphertext
func EncryptAES(plaintext string) (string, error) {
	key, err := base64.StdEncoding.DecodeString(os.Getenv("ENC_KEY"))
	if err != nil || len(key) != 32 {
		return "", errors.New("invalid 32 byte key")
	}

	encKey, macKey, err := deriveKeys(key)
	if err != nil {
		return "", errors.New("error deriving keys")
	}

	iv := make([]byte, aes.BlockSize)
	if _, err := rand.Read(iv); err != nil {
		return "", err
	}

	plaintextBytes := []byte(plaintext)
	plaintextBytes = pkcs7Pad(plaintextBytes, aes.BlockSize)

	block, err := aes.NewCipher(encKey)
	if err != nil {
		return "", err
	}
	ciphertext := make([]byte, len(plaintextBytes))
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext, plaintextBytes)

	h := hmac.New(sha256.New, macKey)
	h.Write(append(iv, ciphertext...))
	mac := h.Sum(nil)

	finalCiphertext := append(append(iv, ciphertext...), mac...)
	return base64.StdEncoding.EncodeToString(finalCiphertext), nil
}

// DecryptAES decrypts ciphertext with embedded IV and verifies HMAC.
func DecryptAES(encrypted string) (string, error) {
	key, err := base64.StdEncoding.DecodeString(os.Getenv("ENC_KEY"))
	if err != nil || len(key) != 32 {
		return "", errors.New("invalid 32 byte AES key")
	}

	data, err := base64.StdEncoding.DecodeString(encrypted)
	if err != nil {
		return "", errors.New("invalid ciphertext")
	}

	if len(data) < aes.BlockSize+sha256.Size {
		return "", errors.New("invalid ciphertext format")
	}

	encKey, macKey, err := deriveKeys(key)
	if err != nil {
		return "", err
	}

	iv := data[:aes.BlockSize]
	ciphertext := data[aes.BlockSize : len(data)-sha256.Size]
	receivedMac := data[len(data)-sha256.Size:]

	h := hmac.New(sha256.New, macKey)
	h.Write(append(iv, ciphertext...))
	if !hmac.Equal(h.Sum(nil), receivedMac) {
		return "", errors.New("HMAC verification failed")
	}

	block, err := aes.NewCipher(encKey)
	if err != nil {
		return "", err
	}
	mode := cipher.NewCBCDecrypter(block, iv)
	plaintext := make([]byte, len(ciphertext))
	mode.CryptBlocks(plaintext, ciphertext)

	plaintext, err = pkcs7Unpad(plaintext, aes.BlockSize)
	if err != nil {
		return "", err
	}
	return string(plaintext), nil
}

// pkcs7Pad pads data to the specified block size using PKCS#7.
func pkcs7Pad(data []byte, blockSize int) []byte {
	padding := blockSize - (len(data) % blockSize)
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

// pkcs7Unpad removes padding with constant-time checks.
func pkcs7Unpad(data []byte, blockSize int) ([]byte, error) {
	if len(data) == 0 || len(data)%blockSize != 0 {
		return nil, errors.New("invalid data length")
	}

	padLen := int(data[len(data)-1])
	if padLen < 1 || padLen > blockSize {
		return nil, errors.New("invalid padding size")
	}

	var invalid byte
	for i := 0; i < padLen; i++ {
		pos := len(data) - 1 - i
		invalid |= data[pos] ^ byte(padLen)
	}

	if invalid != 0 {
		return nil, errors.New("invalid padding content")
	}

	return data[:len(data)-padLen], nil
}

// Replace in EncryptAES/DecryptAES:
func deriveKeys(key []byte) (encKey, macKey []byte, err error) {
	hkdfKeys := hkdf.New(sha256.New, key, nil, []byte("aes-hmac-keys"))
	encKey = make([]byte, 32)
	_, err1 := io.ReadFull(hkdfKeys, encKey)
	if err1 != nil {
		return nil, nil, err1
	}

	macKey = make([]byte, 32)
	_, err2 := io.ReadFull(hkdfKeys, macKey)
	if err2 != nil {
		return nil, nil, err2
	}
	return
}

func IsDuplicateKeyError(err error) bool {
	var mysqlErr *mysql.MySQLError
	if errors.As(err, &mysqlErr) && mysqlErr.Number == 1062 {
		return true
	}
	return false
}

func uploadBase64ToS3(base64Str, bucketName, objectKey string) (string, error) {

	region := os.Getenv("AWS_REGION")
	if region == "" {
		log.Println("Warning: AWS_REGION not set, defaulting to us-west-2")
		region = "us-west-2"
	}

	if os.Getenv("AWS_ACCESS_KEY_ID") == "" || os.Getenv("AWS_SECRET_ACCESS_KEY") == "" {
		return "", fmt.Errorf("AWS credentials not found in environment")
	}

	imageData, err := base64.StdEncoding.DecodeString(base64Str)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}

	staticCredentials := credentials.NewStaticCredentialsProvider("********************", "PRD3Nw9/FuXQNM/SSK3ZwR3ZCtl/NVgQBFLc6zf9", "")

	cfg, err := config.LoadDefaultConfig(
		context.TODO(),
		config.WithRegion(region),
		config.WithCredentialsProvider(staticCredentials),
	)
	if err != nil {
		return "", fmt.Errorf("failed to load AWS config: %w", err)
	}

	s3Client := s3.NewFromConfig(cfg)
	_, err = s3Client.PutObject(context.TODO(), &s3.PutObjectInput{
		Bucket:      aws.String(bucketName),
		Key:         aws.String(objectKey),
		Body:        bytes.NewReader(imageData),
		ContentType: aws.String("image/png"),
	})
	if err != nil {
		return "", fmt.Errorf("failed to upload to S3: %w", err)
	}

	url := fmt.Sprintf("https://%s.s3.%s.amazonaws.com/%s", bucketName, region, objectKey)
	return url, nil
}

func uploadBase64ToGCS(base64Str, bucketName, objectKey string) (string, error) {
	// Decode base64 image
	imageData, err := base64.StdEncoding.DecodeString(base64Str)
	if err != nil {
		return "", fmt.Errorf("failed to decode base64: %w", err)
	}

	ctx := context.Background()

	// Create GCS client using ADC (Application Default Credentials)
	client, err := storage.NewClient(ctx)
	if err != nil {
		return "", fmt.Errorf("failed to create GCS client: %w", err)
	}
	defer client.Close()

	// Get bucket and object handle
	bucket := client.Bucket(bucketName)
	object := bucket.Object(objectKey)

	// Create context with timeout
	ctx, cancel := context.WithTimeout(ctx, time.Second*50)
	defer cancel()

	writer := object.NewWriter(ctx)
	writer.ContentType = "image/png" // You can modify based on image type

	// Upload image
	if _, err := io.Copy(writer, bytes.NewReader(imageData)); err != nil {
		return "", fmt.Errorf("failed to write to GCS object: %w", err)
	}

	// Finalize the upload
	if err := writer.Close(); err != nil {
		return "", fmt.Errorf("failed to close writer: %w", err)
	}

	// Return the GCS object path (not public URL)
	gcsPath := fmt.Sprintf("gs://%s/%s", bucketName, objectKey)
	return gcsPath, nil
}

func checkNullString(s string) *string {
	if s != "" {
		return &s
	}
	return nil
}

// func SaveToken(dbUser models.User, provider string, accessToken string, refreshToken string, expiredAt time.Time) error {
// 	// Step 1: Get or Create TokenType
// 	var tokenType models.TokenType

// 	if err := cfg.DB.Where("token_type = ?", provider).First(&tokenType).Error; err != nil {
// 		if err == gorm.ErrRecordNotFound {
// 			tokenType = models.TokenType{TokenType: provider}
// 			if err := cfg.DB.Create(&tokenType).Error; err != nil {
// 				return err
// 			}
// 		} else {
// 			return err
// 		}
// 	}

// 	// Step 2: Check if a UserToken exists
// 	var userToken models.UserToken
// 	if err := cfg.DB.Where("user_id = ? AND token_type_id = ?", dbUser.Id, tokenType.Id).First(&userToken).Error; err != nil {
// 		if err == gorm.ErrRecordNotFound {
// 			// Step 3: Create new UserToken
// 			userToken = models.UserToken{
// 				UserID:       dbUser.Id,
// 				TokenTypeID:  tokenType.Id,
// 				IsExpired:    false,
// 				ExpiredAt:    expiredAt,
// 				AccessToken:  accessToken,
// 				RefreshToken: refreshToken,
// 			}
// 			if err := cfg.DB.Create(&userToken).Error; err != nil {
// 				return err
// 			}
// 		} else {
// 			return err
// 		}
// 	} else {
// 		userToken = models.UserToken{
// 			IsExpired:    false,
// 			ExpiredAt:    expiredAt,
// 			AccessToken:  accessToken,
// 			RefreshToken: refreshToken,
// 		}
// 		err := cfg.DB.
// 			Where("user_id = ? and  token_type_id = ?", dbUser.Id, tokenType.Id).
// 			Updates(userToken).Error
// 		if err != nil {
// 			return err
// 		}
// 	}
// 	return nil
// }

func GetISO2CodeByName(db *gorm.DB, countryName string) (string, error) {
	var country models.CountryCode
	result := db.Where("name = ?", countryName).First(&country)
	if result.Error != nil {
		return "", fmt.Errorf("country not found: %v", result.Error)
	}
	return country.ISO2Code, nil
}

func reuploadStripeFile(fileID string) (string, error) {
	// 1. Download the file from Stripe
	fileObj, err := file.Get(fileID, nil)
	fmt.Println(fileObj)
	if err != nil {
		return "", err
	}

	client := &http.Client{}
	req, err := http.NewRequest("GET", fileObj.URL, nil)
	if err != nil {
		return "", err
	}
	req.Header.Set("Authorization", "Bearer "+os.Getenv("STRIPE_RESTRICTED_KEY"))

	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("failed to download file: %s", string(body))
	}

	// 3. Read file content into memory
	fileData, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	// 4. Determine file type based on the data
	contentType := resp.Header.Get("Content-Type")
	fileExt := ".jpg" // Default extension

	// If content type is octet-stream, detect the real type
	if contentType == "application/octet-stream" || contentType == "" {
		// Use http.DetectContentType which reads magic numbers
		contentType = http.DetectContentType(fileData)

		// Additional magic number checks for common formats
		if len(fileData) > 4 {
			if bytes.HasPrefix(fileData, []byte{0xFF, 0xD8, 0xFF}) {
				contentType = "image/jpeg"
				fileExt = ".jpg"
			} else if bytes.HasPrefix(fileData, []byte{0x89, 0x50, 0x4E, 0x47}) {
				contentType = "image/png"
				fileExt = ".png"
			} else if bytes.HasPrefix(fileData, []byte{0x47, 0x49, 0x46}) {
				contentType = "image/gif"
				fileExt = ".gif"
			} else if bytes.HasPrefix(fileData, []byte{0x25, 0x50, 0x44, 0x46}) {
				contentType = "application/pdf"
				fileExt = ".pdf"
			}
		}
	} else {
		// Extract extension from content type
		exts, _ := mime.ExtensionsByType(contentType)
		if len(exts) > 0 {
			fileExt = exts[0]
			if !strings.HasPrefix(fileExt, ".") {
				fileExt = "." + fileExt
			}
		}
	}

	// If still octet-stream, force to a supported format (JPEG)
	if contentType == "application/octet-stream" {
		contentType = "image/jpeg"
		fileExt = ".jpg"
	}

	// 5. Create file parameters for reuploading with correct content type
	fileParams := &stripe.FileParams{
		Purpose:    stripe.String(string(stripe.FilePurposeIdentityDocument)),
		FileReader: bytes.NewReader(fileData),
		Filename:   stripe.String("identity_document" + fileExt),
	}

	// Set content type through Stripe's API
	// We can do this by adding a file_link parameter to specify metadata
	contentType = "image/png"
	fileParams.AddMetadata("content-type", contentType)

	// 6. Upload file to Stripe with correct purpose
	newFile, err := file.New(fileParams)
	if err != nil {
		return "", err
	}

	return newFile.ID, nil
}

// LogError logs an error to the database with stack trace and file information
func LogError(err error, serviceName string, userID *uint, requestID string) error {
	if err == nil {
		return nil
	}

	// Get caller information
	_, file, line, ok := runtime.Caller(1)
	if !ok {
		file = "unknown"
		line = 0
	}

	// Get stack trace
	stack := getStackTrace()

	// Create error log entry
	errorLog := models.ErrorLog{
		ServiceName: serviceName,
		FileName:    file,
		LineNumber:  line,
		Error:       err.Error(),
		Stack:       stack,
		UserID:      userID,
		RequestID:   requestID,
		CreatedAt:   time.Now(),
	}

	// Save to database
	if err := cfg.DB.Create(&errorLog).Error; err != nil {
		// If we can't log to database, at least print to console
		fmt.Printf("Failed to log error to database: %v\n", err)
		return err
	}

	return nil
}

// getStackTrace returns the current stack trace as a string
func getStackTrace() string {
	const depth = 32
	var pcs [depth]uintptr
	n := runtime.Callers(3, pcs[:])
	frames := runtime.CallersFrames(pcs[:n])

	var stack strings.Builder
	for {
		frame, more := frames.Next()
		fmt.Fprintf(&stack, "%s\n\t%s:%d\n", frame.Function, frame.File, frame.Line)
		if !more {
			break
		}
	}
	return stack.String()
}

func convertDateStringToUnixTimestamp(dateStr interface{}) (int64, error) {
	// Handle different possible types from MySQL
	var dateString string
	switch v := dateStr.(type) {
	case string:
		dateString = v
	case []byte:
		dateString = string(v)
	case time.Time:
		dateString = v.Format("2006-01-02")
	default:
		return 0, fmt.Errorf("unsupported date type: %T", dateStr)
	}

	// Parse the date string (MySQL returns YYYY-MM-DD format)
	parsedDate, err := time.Parse("2006-01-02", dateString)
	if err != nil {
		return 0, fmt.Errorf("failed to parse date string %s: %v", dateString, err)
	}

	// Convert to UTC midnight and get Unix timestamp
	utcMidnight := time.Date(
		parsedDate.Year(),
		parsedDate.Month(),
		parsedDate.Day(),
		0, 0, 0, 0,
		time.UTC,
	)

	return utcMidnight.Unix(), nil
}