package ResponseDTO

import "time"

type KYCVerificationListResponse struct {
	APIResponse
	Verifications []KYCVerificationDTO `json:"verifications"`
}

type KYCVerificationDTO struct {
	UserID           uint      `json:"user_id"`
	VerificationID   uint      `json:"verification_id"`
	FullName         string    `json:"full_name"`
	RequestDate      time.Time `json:"request_date"`
	WabisabiID       string    `json:"wabisabi_id"`
	VerificationType string    `json:"verification_type"`
}
