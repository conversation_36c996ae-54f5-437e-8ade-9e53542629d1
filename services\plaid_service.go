package services

import (
	"auth-service/config"
	resDto "auth-service/dtos/Response"
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/labstack/echo/v4"
	"github.com/plaid/plaid-go/v14/plaid"
)

// func GetBanksByKeyword(ctx context.Context, query string, count int) ([]plaid.Institution, error) {
// 	client := config.PlaidClient

// 	// Prepare request
// 	req := plaid.NewInstitutionsSearchRequest(query, []plaid.CountryCode{plaid.COUNTRYCODE_US})
// 	req.SetProducts([]plaid.Products{plaid.PRODUCTS_AUTH})

// 	// Call Plaid API
// 	resp, _, err := client.PlaidApi.InstitutionsSearch(ctx).
// 		InstitutionsSearchRequest(*req).
// 		Execute()

// 	if err != nil {
// 		return nil, fmt.Errorf("failed to fetch institutions: %w", err)
// 	}

//		return resp.Institutions, nil
//	}
func GetAllEuropeanBanks(c echo.Context) error {

	count := 10
	offset := 10
	client := config.PlaidClient

	countries := []plaid.CountryCode{
		plaid.COUNTRYCODE_GB,
		plaid.COUNTRYCODE_FR,
		plaid.COUNTRYCODE_ES,
		plaid.COUNTRYCODE_IE,
		plaid.COUNTRYCODE_NL,
	}

	req := plaid.NewInstitutionsGetRequest(int32(count), int32(offset), countries)

	resp, _, err := client.PlaidApi.InstitutionsGet(c.Request().Context()).
		InstitutionsGetRequest(*req).
		Execute()

	if err != nil {
		return fmt.Errorf("failed to fetch institutions: %w", err)
	}
	return c.JSON(http.StatusOK, resp.Institutions)

	//return resp.Institutions, nil
}

func CreatePlaidTokenUser(c echo.Context) error {
	client := config.PlaidClient

	user := plaid.NewLinkTokenCreateRequestUser(fmt.Sprint(c.Get("user_id")))

	req := plaid.NewLinkTokenCreateRequest(
		"Wabisabi Wallet App", // client_name
		"en",                  // language
		[]plaid.CountryCode{ // country_codes
			plaid.COUNTRYCODE_GB,
			plaid.COUNTRYCODE_FR,
			plaid.COUNTRYCODE_ES,
			plaid.COUNTRYCODE_IE,
			plaid.COUNTRYCODE_NL,
		},
		*user, // user
	)

	req.SetProducts([]plaid.Products{
		plaid.PRODUCTS_AUTH,
		plaid.PRODUCTS_TRANSFER,
		// plaid.PRODUCTS_BALANCE,
		// plaid.PRODUCTS_TRANSACTIONS,
	})
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	resp, _, err := client.PlaidApi.LinkTokenCreate(ctx).
		LinkTokenCreateRequest(*req).
		Execute()

	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: err.Error()})
	}

	return c.JSON(http.StatusOK, echo.Map{
		"link_token": resp.GetLinkToken(),
	})
}
func ExchangePublicToken(c echo.Context) error {

	user_id, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: "Error converting user ID"})
	}

	var req struct {
		PublicToken string `json:"public_token"`
	}

	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDto.APIResponse{Message: err.Error()})
	}

	client := config.PlaidClient
	ctx := context.Background()
	resp, _, err := client.PlaidApi.ItemPublicTokenExchange(ctx).ItemPublicTokenExchangeRequest(
		*plaid.NewItemPublicTokenExchangeRequest(req.PublicToken),
	).Execute()

	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDto.APIResponse{Message: err.Error()})
	}
	itemid := resp.GetItemId()
	plaidToken := &PlaidToken{
		AccessToken: resp.GetAccessToken(),
		ExpiredAt:   time.Now().Add(time.Hour),
		UserId:      user_id,
		Provider:    "plaid",
		ItemId:      &itemid,
	}
	err = SaveToken(plaidToken)
	if err != nil {
		fmt.Println("Error saving Plaid token:", err)
	}
	return c.JSON(http.StatusOK, resDto.APIResponse{IsSuccess: true, Message: "Successfully exchanged public token"})
}

// this will get how many accounts user have selected
func GetPlaidAccountId(accessToken string) (accountId string, Error error) {
	client := config.PlaidClient

	// 1. Get all accounts linked to this access_token
	accountsResp, _, err := client.PlaidApi.AccountsGet(context.Background()).
		AccountsGetRequest(plaid.AccountsGetRequest{
			AccessToken: accessToken,
		}).Execute()

	if err != nil {
		log.Fatal("Error getting accounts: ", err)
		return "", err
	}

	// 2. Choose the correct account (e.g., checking)
	accountID := accountsResp.Accounts[0].AccountId // Ideally, let user pick one

	return accountID, nil
}
