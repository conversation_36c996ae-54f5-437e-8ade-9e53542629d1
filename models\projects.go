package models

import "gorm.io/gorm"

type Projects struct {
	gorm.Model

	Name                 string  `gorm:"size:255;not null" json:"name"`
	Description          *string `gorm:"type:text" json:"description"`
	CreatedBy            uint    `gorm:"not null" json:"created_by"`
	SmartContractAddress *string `gorm:"size:255" json:"smart_contract_address"`

	Creator User `gorm:"foreignKey:CreatedBy;references:Id" json:"creator"`
}
