package models

import "time"

type Agreement struct {
	MessageID   uint      `gorm:"primaryKey;not null" json:"message_id"`
	Title       string    `gorm:"size:255;not null" json:"title"`
	Description string    `gorm:"type:text" json:"description"`
	CreatedAt   time.Time `json:"created_at"`
	Status      string    `gorm:"not null;type:enum('draft','pending','active','completed','cancelled')" json:"status"`
	Message     Message   `gorm:"foreignKey:MessageID;references:ID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE;" json:"-"`
}