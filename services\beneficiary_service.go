package services

import (
	reqDTO "auth-service/dtos/Request"
	resDTO "auth-service/dtos/Response"
	"fmt"
	"strconv"
	"strings"

	"auth-service/config"
	"net/http"

	"github.com/labstack/echo/v4"
)

// When user want to add beneficiary by entering wsid
func AddBeneficiary(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		message := "Error converting user id"
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}
	userEmail := c.Get("email").(string)

	var req reqDTO.AddBeneficiaryRequestDTO
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Invalid input"})
	}

	receiver, err := findUserbyWsId(req.ReceiverWSID)
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "WSID does not belong to any user"})
	}

	if userID == receiver.Id {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "You cannot add yourself as a beneficiary"})
	}

	if checkBeneficiaryExistsAlready(userID, receiver.Id) {
		return c.JSON(http.StatusConflict, resDTO.APIResponse{Message: "Beneficiary already added"})
	}

	if err := createNewBeneficiary(userID, req, *receiver); err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: err.Error()})
	}

	err1 := BroadcastNotification(userID, "beneficiary_added",
		"New Beneficiary Added",
		fmt.Sprintf("You have added %s as a beneficiary", receiver.FirstName),
		map[string]interface{}{
			"beneficiary_name": receiver.FirstName + " " + receiver.LastName,
			"beneficiary_type": req.BeneficiaryTypeId,
		})
	if err1 != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Could not broadcast message"})
	}

	err2 := SendOTPEmail(userEmail, "Payment sent", "Payment has been initiated successfully")
	if err2 != nil {

		fmt.Println("Error sending email:", err2)
	}

	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: "Beneficiary added successfully"})
}

// user saved beneficiaries by user_id
// func GetBeneficiaries(c echo.Context) error {
// 	userID, err := InterfaceToUint(c.Get("user_id"))
// 	if err != nil {
// 		message := "Error converting user id"
// 		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
// 	}
// 	var beneficiaries []models.Beneficiaries
// 	if err := config.DB.Where("user_id = ?", userID).Find(&beneficiaries).Error; err != nil {
// 		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Could not fetch beneficiaries"})
// 	}
// 	return c.JSON(http.StatusOK, beneficiaries)
// }

// return all beneficiaries of a single user
func GetBeneficiaries(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error converting user id"})
	}

	beneficiaryTypeIdStr := c.QueryParam("beneficiary_type_id")
	if beneficiaryTypeIdStr == "" {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "beneficiary_type_id is required"})
	}

	beneficiaryTypeIdUint64, err := strconv.ParseUint(beneficiaryTypeIdStr, 10, 32)
	if err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "beneficiary_type_id must be a valid unsigned integer"})
	}
	beneficiaryTypeId := uint(beneficiaryTypeIdUint64)

	beneficiariesWalletMapDTO, err := GetUsersBeneficiaries(config.DB, userID, beneficiaryTypeId)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Could not fetch beneficiaries"})
	}
	//TODO: This should not be hardcodes get id if == ALL
	if beneficiaryTypeId == 6 {
		beneficiariesWalletMapDTO, err = GetAllUsersBeneficiaries(config.DB, userID, beneficiaryTypeId)
		if err != nil {
			return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Could not fetch beneficiaries"})
		}
	}

	response := resDTO.APIResponse{IsSuccess: true, Message: "Beneficiaries fetched successfully"}

	beneficiariesResponse := resDTO.BeneficiaryWalletResponseDTO{
		APIResponse:             response,
		BeneficiaryWalletMapDTO: *beneficiariesWalletMapDTO,
	}

	return c.JSON(http.StatusOK, beneficiariesResponse)
}

// when user click on savedBeneficiaryDetails
func GetBeneficiaryDetails(c echo.Context) error {
	// Convert `user_id` from context
	userID, err := InterfaceToUint(c.Get("user_id"))
	b_Id := c.Param("b_id")
	print(b_Id)

	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error converting user ID"})
	}

	// Bind request
	//METHOD CHANGED FROM POST TO GET
	// req := struct {
	// 	BeneficiaryID uint `json:"beneficiary_id"`
	// }{}

	// if err := c.Bind(&req); err != nil {
	// 	return c.JSON(http.StatusBadRequest, res.APIResponse{Message: "Invalid input"})
	// }

	// Fetch all details in a single query
	// bIDUint, err := strconv.ParseUint(b_Id, 10, 32)
	// if err != nil {
	// 	return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "Invalid beneficiary ID"})
	// }
	beneficiaryDetails, err := GetBeneficiaryDetailsFromDB(config.DB, userID, b_Id)

	if err != nil || beneficiaryDetails == nil {
		return c.JSON(http.StatusNotFound, resDTO.APIResponse{Message: "Beneficiary details not found"})
	}

	response := resDTO.APIResponse{IsSuccess: true, Message: "Beneficiary details fetched successfully"}

	beneficiaryDetailsResponseDTO := resDTO.BeneficiaryDetailsResponse{
		APIResponse:        response,
		BeneficiaryDetails: beneficiaryDetails,
	}

	// Return the response
	return c.JSON(http.StatusOK, beneficiaryDetailsResponseDTO)
}

func GetBeneficiaryTypes(c echo.Context) error {
	c.Set("serviceName", "beneficiary_type_service")

	beneficiaryTypes, err := GetBeneficiaryTypesFromDB(config.DB)
	if err != nil {
		c.Set("customMessage", "Failed to fetch beneficiary types")
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Failed to fetch beneficiary types",
		})
	}

	// Convert to DTO
	beneficiaryTypeDTOs := make([]resDTO.BeneficiaryTypeDTO, len(beneficiaryTypes))
	for i, bt := range beneficiaryTypes {
		beneficiaryTypeDTOs[i] = resDTO.BeneficiaryTypeDTO{
			ID:   bt.Id,
			Name: bt.Name,
		}
	}

	c.Set("customMessage", "Beneficiary types fetched successfully")
	return c.JSON(http.StatusOK, resDTO.BeneficiaryTypeResponse{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "Beneficiary types fetched successfully",
		},
		BeneficiaryTypes: beneficiaryTypeDTOs,
	})
}

func UpdateBeneficiaryType(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		message := "Error converting user id"
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}
	c.Set("serviceName", "beneficiary_service")

	var req reqDTO.UpdateBeneficiaryTypeRequest
	if err := c.Bind(&req); err != nil {
		c.Set("customMessage", "Invalid request")
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			Message: "Invalid request",
		})
	}

	// Validate request
	if req.BeneficiaryID == 0 || req.BeneficiaryTypeID == 0 {
		c.Set("customMessage", "Beneficiary ID and Beneficiary Type ID are required")
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			Message: "Beneficiary ID and Beneficiary Type ID are required",
		})
	}

	// Update beneficiary type
	if err := UpdateBeneficiaryTypeDB(config.DB, req.BeneficiaryID, req.BeneficiaryTypeID, nil, userID); err != nil {
		c.Set("customMessage", "Failed to update beneficiary type")
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Failed to update beneficiary type",
		})
	}

	c.Set("customMessage", "Beneficiary type updated successfully")
	return c.JSON(http.StatusOK, resDTO.APIResponse{
		IsSuccess: true,
		Message:   "Beneficiary type updated successfully",
	})
}

func UpdateFavBeneficiaryType(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		message := "Error converting user id"
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}
	c.Set("serviceName", "beneficiary_service")

	isFavStr := c.QueryParam("is_fav")

	var is_fav *bool
	if isFavStr != "" {
		val := strings.ToLower(isFavStr) == "true" || isFavStr == "1"
		is_fav = &val
	}
	beneficiary_id := c.QueryParam("beneficiary_id")

	var beneficiaryIDUint uint
	if beneficiary_id != "" {
		idUint64, err := strconv.ParseUint(beneficiary_id, 10, 32)
		if err != nil {
			return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
				Message: "Invalid beneficiary_id",
			})
		}
		beneficiaryIDUint = uint(idUint64)
	} else {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			Message: "beneficiary_id is required",
		})
	}

	// Update beneficiary type
	if err := UpdateBeneficiaryTypeDB(config.DB, beneficiaryIDUint, 0, is_fav, userID); err != nil {
		c.Set("customMessage", "Failed to update beneficiary type")
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			Message: "Failed to update beneficiary type",
		})
	}

	c.Set("customMessage", "Beneficiary type updated successfully")
	return c.JSON(http.StatusOK, resDTO.APIResponse{
		IsSuccess: true,
		Message:   "Beneficiary type updated successfully",
	})
}

func DeleteBeneficiary(c echo.Context) error {
	c.Set("serviceName", "beneficiary_service")
	c.Set("moduleName", "services")

	// Get user ID from context
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			IsSuccess: false,
			Message:   "Error converting user ID",
		})
	}

	// Bind request body
	var req reqDTO.DeleteBeneficiaryRequestDTO
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			IsSuccess: false,
			Message:   "Invalid request body",
		})
	}

	// Validate request
	if req.BeneficiaryID == 0 {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{
			IsSuccess: false,
			Message:   "Beneficiary ID is required",
		})
	}

	// Delete beneficiary
	err = DeleteBeneficiaryDB(userID, req.BeneficiaryID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{
			IsSuccess: false,
			Message:   err.Error(),
		})
	}

	return c.JSON(http.StatusOK, resDTO.APIResponse{
		IsSuccess: true,
		Message:   "Beneficiary deleted successfully",
	})
}

func SearchBeneficiary(c echo.Context, isFav bool) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Invalid user ID"})
	}

	searchTerm := c.QueryParam("search")
	if searchTerm == "" {
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: "'search' query cannot be empty"})
	}

	results, err := SearchBeneficiaries(userID, searchTerm, isFav)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Search failed"})
	}

	response := resDTO.BeneficiaryWalletResponseDTO{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "successfully fetched beneficiaries",
		},
		BeneficiaryWalletMapDTO: results,
	}

	return c.JSON(http.StatusOK, response)
}

func GetFavBeneficiaries(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error converting user id"})
	}

	result, err := getUserFavBeneficiaries(userID)

	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Could not fetch favorite beneficiaries"})
	}

	response := resDTO.APIResponse{IsSuccess: true, Message: "Favorite beneficiaries fetched successfully"}
	beneficiariesResponse := resDTO.BeneficiaryWalletResponseDTO{
		APIResponse:             response,
		BeneficiaryWalletMapDTO: result,
	}

	return c.JSON(http.StatusOK, beneficiariesResponse)
}
