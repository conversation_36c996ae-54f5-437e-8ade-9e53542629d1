package config

import (
	"auth-service/models"
	"fmt"
	"log"
	"os"

	"github.com/joho/godotenv"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var DB *gorm.DB

func ConnectDB() {
	var err error
	// config, errCfg := GetEnvConfig(".")
	// if errCfg != nil {
	// 	log.Fatal("Cannot load database config:", errCfg)

	// }
	// fmt.Printf("Active Profile: %s\n", config.ENVIRONMENT)
	// // fmt.Println("Active Profile: ", config.ENVIRONMENT)
	if os.Getenv("ACTIVE_PROFILE") == "PRODUCTION" {
		err := godotenv.Load("prod.env")
		if err != nil {
			fmt.Println("Error loading env", err)
			panic("Error loading .env file")
		}
	} else {
		err := godotenv.Load("dev.env")
		if err != nil {
			fmt.Println("Error loading env", err)
			panic("Error loading .env file")
		}
	}
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")
	dbName := os.Getenv("DB_NAME")
	// Create the connection string

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
		dbUser, dbPassword, dbHost, dbPort, dbName)
	// Connect to the database
	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to the database:", err)
	}

	// else {
	// 	err := godotenv.Load("prod.env")
	// 	if err != nil {
	// 		fmt.Println("Error loading env", err)
	// 		panic("Error loading .env file")
	// 	}
	// 	dbHost := os.Getenv("DB_HOST")
	// 	dbPort := os.Getenv("DB_PORT")
	// 	dbUser := os.Getenv("DB_USER")
	// 	dbPassword := os.Getenv("DB_PASSWORD")
	// 	dbName := os.Getenv("DB_NAME")
	// 	// Create the connection string
	// 	print(dbHost, dbPort, dbUser, dbPassword, dbName)
	// 	dsn := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local",
	// 		dbUser, dbPassword, dbHost, dbPort, dbName)
	// 	// Connect to the database
	// 	DB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	// 	if err != nil {
	// 		log.Fatal("Failed to connect to the database:", err)
	// 	}
	// }

	err = DB.AutoMigrate(
		&models.User{},
		&models.UserDetails{},
		&models.BankDetails{},
		&models.Question{},
		&models.QuestionsAttempt{},
		&models.Transaction{},
		&models.Wallet{},
		&models.Beneficiaries{},
		&models.NicDetails{},
		&models.StaticConfiguration{},
		&models.UserToken{},
		&models.TokenType{},
		&models.Message{},
		&models.TextMessage{},
		&models.Agreement{},
		&models.AgreementUser{},
		&models.Milestones{},
		&models.UserContact{},
		&models.CountryCode{},
		&models.StripeConnectedAccount{},
		&models.KYCVerification{},
		&models.StripePaymentMethod{},
		&models.ErrorLog{},
		&models.Admin{},
		&models.PaymentType{},
		&models.BeneficiaryType{},
		&models.Notification{},
		&models.CardIcons{},
		&models.GraphKeys{},
		&models.Conversation{},
		&models.ConversationParticipant{},
		&models.Projects{},
		&models.Media{},
		&models.EstimatedAnnualIncome{},
		&models.Occupation{},
		&models.Charge{},
	)

	if err != nil {
		log.Fatalf("Failed to migrate database: %v", err)
	}

	// if os.Getenv("ACTIVE_PROFILE") != "PRODUCTION" {
	// 	SeedDB()
	// }

	if err := SeedDB(); err != nil {
		log.Fatalf("Database seeding failed: %v", err)
	}

	log.Println("Database connection established")
}

func SeedDB() error {

	// Seed Occupations
	occupations := []models.Occupation{
		{Name: "Engineer"},
		{Name: "Doctor"},
		{Name: "Teacher"},
		{Name: "Lawyer"},
		{Name: "Shopkeeper"},
		{Name: "Freelancer"},
		{Name: "Civil Servant"},
		{Name: "Waiter/Waitress"},
		{Name: "IT Technician"},
		{Name: "Student"},
	}
	for _, o := range occupations {
		if err := DB.FirstOrCreate(&o, models.Occupation{Name: o.Name}).Error; err != nil {
			return fmt.Errorf("failed to seed Occupation [%s]: %w", o.Name, err)
		}
	}

	// Seed Estimated Annual Incomes
	incomes := []models.EstimatedAnnualIncome{
		{Label: "Less than $20,000"},
		{Label: "$20,000 - $49,999"},
		{Label: "$50,000 - $99,999"},
		{Label: "$100,000 or more"},
	}
	for _, income := range incomes {
		if err := DB.FirstOrCreate(&income, models.EstimatedAnnualIncome{Label: income.Label}).Error; err != nil {
			return fmt.Errorf("failed to seed EstimatedAnnualIncome [%s]: %w", income.Label, err)
		}
	}

	// Seed StaticConfigurations

	staticConfigs := []models.StaticConfiguration{
		{Code: "SMTP_HOST", Value: "mail.devcustomprojects.com"},
		{Code: "SMTP_PORT", Value: "465"},
		{Code: "SMTP_USERNAME", Value: "<EMAIL>"},
		{Code: "SMTP_PASSWORD", Value: "s{8QBiBv8_;h"},
	}

	for _, config := range staticConfigs {
		if err := DB.FirstOrCreate(&config, models.StaticConfiguration{Code: config.Code}).Error; err != nil {
			return fmt.Errorf("failed to seed StaticConfiguration [%s]: %w", config.Code, err)
		}
	}

	// Seed BeneficiaryTypes

	beneficiaryTypes := []models.BeneficiaryType{
		{Id: 1, Name: "Family"},
		{Id: 2, Name: "Fav"},
		{Id: 3, Name: "Friend"},
		{Id: 4, Name: "Others"},
		{Id: 6, Name: "All"},
	}

	for _, btype := range beneficiaryTypes {
		if err := DB.FirstOrCreate(&btype, models.BeneficiaryType{Id: btype.Id}).Error; err != nil {
			return fmt.Errorf("failed to seed BeneficiaryType [%s]: %w", btype.Name, err)
		}
	}

	// Seed CardIcons

	cardIcons := []models.CardIcons{
		{
			BrandName: "visa",
			Icon:      `PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCA0NiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMC41IiB5PSIwLjUiIHdpZHRoPSI0NC4zMzMzIiBoZWlnaHQ9IjMxIiByeD0iMy41IiBmaWxsPSJ3aGl0ZSIgZmlsbC1vcGFjaXR5PSIwLjEiLz4KPHJlY3QgeD0iMC41IiB5PSIwLjUiIHdpZHRoPSI0NC4zMzMzIiBoZWlnaHQ9IjMxIiByeD0iMy41IiBzdHJva2U9IiNFOEU4RTgiLz4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik0xNC4zMzQxIDIxLjE0NTFIMTEuNTg3N0w5LjUyODMzIDEzLjA1NzNDOS40MzA1OCAxMi42ODUyIDkuMjIzMDQgMTIuMzU2MyA4LjkxNzc0IDEyLjIwMTNDOC4xNTU4NCAxMS44MTE4IDcuMzE2MjggMTEuNTAxNyA2LjQwMDM5IDExLjM0NTRWMTEuMDM0SDEwLjgyNDVDMTEuNDM1MSAxMS4wMzQgMTEuODkzIDExLjUwMTcgMTEuOTY5NCAxMi4wNDVMMTMuMDM3OSAxNy44NzlMMTUuNzgyOSAxMS4wMzRIMTguNDUyOUwxNC4zMzQxIDIxLjE0NTFaTTE5Ljk3OTQgMjEuMTQ1MUgxNy4zODU3TDE5LjUyMTQgMTEuMDM0SDIyLjExNTFMMTkuOTc5NCAyMS4xNDUxWk0yNS40NzA3IDEzLjgzNTFDMjUuNTQ3IDEzLjI5MDUgMjYuMDA0OSAxMi45NzkxIDI2LjUzOTIgMTIuOTc5MUMyNy4zNzg4IDEyLjkwMDkgMjguMjkzMyAxMy4wNTczIDI5LjA1NjUgMTMuNDQ1NUwyOS41MTQ1IDExLjI2ODVDMjguNzUxMyAxMC45NTcxIDI3LjkxMTcgMTAuODAwOCAyNy4xNDk4IDEwLjgwMDhDMjQuNjMyNCAxMC44MDA4IDIyLjgwMDcgMTIuMjAxMyAyMi44MDA3IDE0LjE0NTFDMjIuODAwNyAxNS42MjM4IDI0LjA5ODIgMTYuNDAwMyAyNS4wMTQxIDE2Ljg2OEMyNi4wMDQ5IDE3LjMzNDQgMjYuMzg2NiAxNy42NDU4IDI2LjMxMDIgMTguMTEyMkMyNi4zMTAyIDE4LjgxMTggMjUuNTQ3IDE5LjEyMzIgMjQuNzg1MSAxOS4xMjMyQzIzLjg2OTIgMTkuMTIzMiAyMi45NTMzIDE4Ljg5IDIyLjExNTEgMTguNTAwNEwyMS42NTcxIDIwLjY3ODdDMjIuNTczIDIxLjA2NjkgMjMuNTYzOSAyMS4yMjMzIDI0LjQ3OTggMjEuMjIzM0MyNy4zMDI0IDIxLjMwMDEgMjkuMDU2NSAxOS45MDA5IDI5LjA1NjUgMTcuODAwOEMyOS4wNTY1IDE1LjE1NjEgMjUuNDcwNyAxNS4wMDExIDI1LjQ3MDcgMTMuODM1MVpNMzguMTMzNyAyMS4xNDUxTDM2LjA3NDMgMTEuMDM0SDMzLjg2MjNDMzMuNDA0MyAxMS4wMzQgMzIuOTQ2NCAxMS4zNDU0IDMyLjc5MzcgMTEuODExOEwyOC45ODAyIDIxLjE0NTFIMzEuNjUwMkwzMi4xODMxIDE5LjY2NzdIMzUuNDYzN0wzNS43NjkgMjEuMTQ1MUgzOC4xMzM3Wk0zNC4yNDM5IDEzLjc1NjlMMzUuMDA1OCAxNy41Njc2SDMyLjg3MDFMMzQuMjQzOSAxMy43NTY5WiIgZmlsbD0iIzE3MkI4NSIvPgo8L3N2Zz4K`,
		},
		{
			BrandName: "mastercard",
			Icon:      `PHN2ZyB3aWR0aD0iNDYiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCA0NiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3QgeD0iMC41IiB5PSIwLjUiIHdpZHRoPSI0NC4zMzMzIiBoZWlnaHQ9IjMxIiByeD0iMy41IiBmaWxsPSJ3aGl0ZSIgZmlsbC1vcGFjaXR5PSIwLjEiLz4KPHJlY3QgeD0iMC41IiB5PSIwLjUiIHdpZHRoPSI0NC4zMzMzIiBoZWlnaHQ9IjMxIiByeD0iMy41IiBzdHJva2U9IiNFOEU4RTgiLz4KPHBhdGggZD0iTTI4Ljc2OTUgNi43MDcwM0MzMy43NjI3IDYuNzA3MDMgMzcuODEwNSAxMC43MDY5IDM3LjgxMDUgMTUuNjQwNkMzNy44MTA0IDIwLjU3NDIgMzMuNzYyNiAyNC41NzMyIDI4Ljc2OTUgMjQuNTczMkMyNi41MzExIDI0LjU3MzIgMjQuNDg0MiAyMy43NjggMjIuOTA1MyAyMi40MzY1QzIxLjMyNjQgMjMuNzY4IDE5LjI3OTUgMjQuNTczMiAxNy4wNDEgMjQuNTczMkMxMi4wNDc5IDI0LjU3MzIgOC4wMDAxNCAyMC41NzQyIDggMTUuNjQwNkM4IDEwLjcwNjkgMTIuMDQ3OSA2LjcwNzAzIDE3LjA0MSA2LjcwNzAzQzE5LjI3OTQgNi43MDcwNSAyMS4zMjY0IDcuNTEyMzkgMjIuOTA1MyA4Ljg0Mzc1QzI0LjQ4NDEgNy41MTIzOSAyNi41MzEyIDYuNzA3MDcgMjguNzY5NSA2LjcwNzAzWiIgZmlsbD0iI0VEMDAwNiIvPgo8cGF0aCBkPSJNMjguNzY5NSA2LjcwNzAzQzMzLjc2MjcgNi43MDcwMyAzNy44MTA1IDEwLjcwNjkgMzcuODEwNSAxNS42NDA2QzM3LjgxMDQgMjAuNTc0MiAzMy43NjI2IDI0LjU3MzIgMjguNzY5NSAyNC41NzMyQzI2LjUzMTEgMjQuNTczMiAyNC40ODQyIDIzLjc2OCAyMi45MDUzIDIyLjQzNjVDMjQuODQ4MiAyMC43OTggMjYuMDgyIDE4LjM2MjQgMjYuMDgyIDE1LjY0MDZDMjYuMDgyIDEyLjkxODYgMjQuODQ4NCAxMC40ODIzIDIyLjkwNTMgOC44NDM3NUMyNC40ODQyIDcuNTEyMzkgMjYuNTMxMiA2LjcwNzA2IDI4Ljc2OTUgNi43MDcwM1oiIGZpbGw9IiNGOUEwMDAiLz4KPHBhdGggZD0iTTIyLjkwNDMgOC44NDM3NUMyNC44NDc3IDEwLjQ4MjMgMjYuMDgxMSAxMi45MTg0IDI2LjA4MTEgMTUuNjQwNkMyNi4wODEgMTguMzYyNiAyNC44NDc1IDIwLjc5OCAyMi45MDQzIDIyLjQzNjVDMjAuOTYxNiAyMC43OTgxIDE5LjcyODYgMTguMzYyMiAxOS43Mjg1IDE1LjY0MDZDMTkuNzI4NSAxMi45MTg4IDIwLjk2MTQgMTAuNDgyMyAyMi45MDQzIDguODQzNzVaIiBmaWxsPSIjRkY1RTAwIi8+Cjwvc3ZnPgo=`,
		},
	}

	for _, icon := range cardIcons {
		if err := DB.FirstOrCreate(&icon, models.CardIcons{BrandName: icon.BrandName}).Error; err != nil {
			return fmt.Errorf("failed to seed CardIcon [%s]: %w", icon.BrandName, err)
		}
	}

	// Seed CountryCode

	countryCodes := []models.CountryCode{
		{Name: "Albania", ISO2Code: "AL"},
		{Name: "Andorra", ISO2Code: "AD"},
		{Name: "Armenia", ISO2Code: "AM"},
		{Name: "Austria", ISO2Code: "AT"},
		{Name: "Azerbaijan", ISO2Code: "AZ"},
		{Name: "Belarus", ISO2Code: "BY"},
		{Name: "Belgium", ISO2Code: "BE"},
		{Name: "Bosnia and Herzegovina", ISO2Code: "BA"},
		{Name: "Bulgaria", ISO2Code: "BG"},
		{Name: "Croatia", ISO2Code: "HR"},
		{Name: "Cyprus", ISO2Code: "CY"},
		{Name: "Czech Republic", ISO2Code: "CZ"},
		{Name: "Denmark", ISO2Code: "DK"},
		{Name: "Estonia", ISO2Code: "EE"},
		{Name: "Finland", ISO2Code: "FI"},
		{Name: "France", ISO2Code: "FR"},
		{Name: "Georgia", ISO2Code: "GE"},
		{Name: "Germany", ISO2Code: "DE"},
		{Name: "Greece", ISO2Code: "GR"},
		{Name: "Hungary", ISO2Code: "HU"},
		{Name: "Iceland", ISO2Code: "IS"},
		{Name: "Ireland", ISO2Code: "IE"},
		{Name: "Italy", ISO2Code: "IT"},
		{Name: "Kazakhstan", ISO2Code: "KZ"},
		{Name: "Kosovo", ISO2Code: "XK"},
		{Name: "Latvia", ISO2Code: "LV"},
		{Name: "Liechtenstein", ISO2Code: "LI"},
		{Name: "Lithuania", ISO2Code: "LT"},
		{Name: "Luxembourg", ISO2Code: "LU"},
		{Name: "Malta", ISO2Code: "MT"},
		{Name: "Moldova", ISO2Code: "MD"},
		{Name: "Monaco", ISO2Code: "MC"},
		{Name: "Montenegro", ISO2Code: "ME"},
		{Name: "Netherlands", ISO2Code: "NL"},
		{Name: "North Macedonia", ISO2Code: "MK"},
		{Name: "Norway", ISO2Code: "NO"},
		{Name: "Poland", ISO2Code: "PL"},
		{Name: "Portugal", ISO2Code: "PT"},
		{Name: "Romania", ISO2Code: "RO"},
		{Name: "Russia", ISO2Code: "RU"},
		{Name: "San Marino", ISO2Code: "SM"},
		{Name: "Serbia", ISO2Code: "RS"},
		{Name: "Slovakia", ISO2Code: "SK"},
		{Name: "Slovenia", ISO2Code: "SI"},
		{Name: "Spain", ISO2Code: "ES"},
		{Name: "Sweden", ISO2Code: "SE"},
		{Name: "Switzerland", ISO2Code: "CH"},
		{Name: "Turkey", ISO2Code: "TR"},
		{Name: "Ukraine", ISO2Code: "UA"},
		{Name: "United Kingdom", ISO2Code: "GB"},
		{Name: "Vatican City", ISO2Code: "VA"},
	}

	for _, cc := range countryCodes {
		if err := DB.FirstOrCreate(&cc, models.CountryCode{ISO2Code: cc.ISO2Code}).Error; err != nil {
			return fmt.Errorf("failed to seed CountryCode [%s]: %w", cc.ISO2Code, err)
		}
	}

	// Seed GraphKeys

	graphKeys := []models.GraphKeys{
		{KeyValue: "today"},
		{KeyValue: "yesterday"},
		{KeyValue: "7_days"},
	}

	for _, key := range graphKeys {
		if err := DB.FirstOrCreate(&key, models.GraphKeys{KeyValue: key.KeyValue}).Error; err != nil {
			return fmt.Errorf("failed to seed GraphKey [%s]: %w", key.KeyValue, err)
		}
	}

	// Seed Payment Types

	paymentTypes := []models.PaymentType{
		{Purpose: "Fuel", Icon: "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"},
		{Purpose: "Food", Icon: "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"},
		{Purpose: "Misc", Icon: "iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAASKSURBVHgB7VtLcttGEO0hmGUqzA3gE1hcRlWiwRNEPkF8A9OfVHknapeqWDF1AsknMH0CIbJLWZI5QaATBI6yCjmYvCZIeQCCMwAEygyCV8UigAFm+k33fNh4JGrQoFYQVAKeN+wQ3XhRJB4ppXBMrhDk0tYgQrQT4iAUQvzeaqkp0de+7w9DKohChPf3f9xznOgNDj3aDZw7zlfHvv9TkPcBJ++Nvd6Lp0KoMQ5d2h3sKRUNXPc7ur7+7dc8D+TycK/38gghNTRWJCigLUEp4mHTIWP7anh5+csxWWAl7HmvXClnf6QuYyzRWxjiO46c+v4ooHuA5w3c+byN+UI9QfuYP5LRppTof/z42jfV0SYLpJyf6eeYNKat1rwPkoUnjLti2bH88Zl8FDnvQHqPbm1TR1xmqsPo4QzvBvBo/748agNId6R02L7bcId9D0z2tcgAKf85TF4Rp7tClsFRhog71a9J2T40PWMkDIKP9DPHET7tGOZzMU5eUQ9N9xsJo/dc7TT0/Z+ntGO4ulrYpM8nnul+I2EsRe7qGOQD2lFgxtYJG5cvS0h/fni5tdtJYDkKtFMj4Y3LEk/7Uiaq/UQWeN5LD/d1sva5XCalcKkEsJ3FcDoZb75Dpdoadjbts63r8GeIP02lBwfPn0ipFmu2EH/zuOqulykqA+74Xu/5MXZSw6xyhPQnlag6ZC9nEraFdG5gjHurY4T/HkdIVllZIGx/oApQGWH0sB5ygb5ep8rK1v+eKkCBkDbjw4eT8f7+oIufa/gJORtvKqMSQMQEtj1yXlRGmHF1NeKxOy1adp+oLKT/K2gI1x3/O8KVTlpfbqeVH5UR/pI7rSJodlpl0ey0NpRRCTQ7rTugWYfrjoZw3dEQrjsawnVHQ7juaAjXHQ3huqPinFac5ahCFhGr/RYvxahKmcWdCbNhUfTXU6RgBsg9LQw8OHjBX2P84H9WRCUX18fJPnUk5Y230s2hvgBponGr1T7Nqg/ZkG8oJ0whnXrdqL6lNeNeuVF0MwHZIa2/iD5kBRCSb0eUE6z2A9kLWpctuMh6oENnF9zm+pMi0XYpFU+skNHVdUmBS0x2dpEWh6XBnQEPHZIFnNlEXSPLbW42aaVptYQxq2LReCRSMp2V4bEnZpN1sguVzzhDhniW7ZkYXAZD05HAEXbO9aWuL0hzB61soYQ0I7omA2xj+C0+unfegXS41D4mjIMnH68SbUuVnO79DozknHU/q5GMSEkI4GJyQlcEunwOW95k2GLMkFq1lujBiS7vy0Aopewvk3S3iDUizoS03ocXR5eXr58l618TroYg202Pw2V0nZFZtBIgQ/rAUG5fh1st+XizUlawuLSbJstgg6NIJdStPPHok1iWSjeKxHHWpMOpXm7LoNpdRAVZkEs+vPTWEB56yO+OeGwjhE/z5IrhmXN8pd8arFaAhLdQL+o8GZC1zkWIf78SzkURvW+356M8gtdSfwEoAhaAYjxPbLM5xR7qblulu/WtJRPAsOgvZ/ANiIfGfUiSt+5hHclQLDY0GjRokIl/AZe4aMYZ7Ye9AAAAAElFTkSuQmCC"},
		{Purpose: "Top Up", Icon: "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"},
		{Purpose: "Utility Bills", Icon: "iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAACE4AAAhOAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAASKSURBVHgB7VtLcttGEO0hmGUqzA3gE1hcRlWiwRNEPkF8A9OfVHknapeqWDF1AsknMH0CIbJLWZI5QaATBI6yCjmYvCZIeQCCMwAEygyCV8UigAFm+k33fNh4JGrQoFYQVAKeN+wQ3XhRJB4ppXBMrhDk0tYgQrQT4iAUQvzeaqkp0de+7w9DKohChPf3f9xznOgNDj3aDZw7zlfHvv9TkPcBJ++Nvd6Lp0KoMQ5d2h3sKRUNXPc7ur7+7dc8D+TycK/38gghNTRWJCigLUEp4mHTIWP7anh5+csxWWAl7HmvXClnf6QuYyzRWxjiO46c+v4ooHuA5w3c+byN+UI9QfuYP5LRppTof/z42jfV0SYLpJyf6eeYNKat1rwPkoUnjLti2bH88Zl8FDnvQHqPbm1TR1xmqsPo4QzvBvBo/748agNId6R02L7bcId9D0z2tcgAKf85TF4Rp7tClsFRhog71a9J2T40PWMkDIKP9DPHET7tGOZzMU5eUQ9N9xsJo/dc7TT0/Z+ntGO4ulrYpM8nnul+I2EsRe7qGOQD2lFgxtYJG5cvS0h/fni5tdtJYDkKtFMj4Y3LEk/7Uiaq/UQWeN5LD/d1sva5XCalcKkEsJ3FcDoZb75Dpdoadjbts63r8GeIP02lBwfPn0ipFmu2EH/zuOqulykqA+74Xu/5MXZSw6xyhPQnlag6ZC9nEraFdG5gjHurY4T/HkdIVllZIGx/oApQGWH0sB5ygb5ep8rK1v+eKkCBkDbjw4eT8f7+oIufa/gJORtvKqMSQMQEtj1yXlRGmHF1NeKxOy1adp+oLKT/K2gI1x3/O8KVTlpfbqeVH5UR/pI7rSJodlpl0ey0NpRRCTQ7rTugWYfrjoZw3dEQrjsawnVHQ7juaAjXHQ3huqPinFac5ahCFhGr/RYvxahKmcWdCbNhUfTXU6RgBsg9LQw8OHjBX2P84H9WRCUX18fJPnUk5Y230s2hvgBponGr1T7Nqg/ZkG8oJ0whnXrdqL6lNeNeuVF0MwHZIa2/iD5kBRCSb0eUE6z2A9kLWpctuMh6oENnF9zm+pMi0XYpFU+skNHVdUmBS0x2dpEWh6XBnQEPHZIFnNlEXSPLbW42aaVptYQxq2LReCRSMp2V4bEnZpN1sguVzzhDhniW7ZkYXAZD05HAEXbO9aWuL0hzB61soYQ0I7omA2xj+C0+unfegXS41D4mjIMnH68SbUuVnO79DozknHU/q5GMSEkI4GJyQlcEunwOW95k2GLMkFq1lujBiS7vy0Aopewvk3S3iDUizoS03ocXR5eXr58l618TroYg202Pw2V0nZFZtBIgQ/rAUG5fh1st+XizUlawuLSbJstgg6NIJdStPPHok1iWSjeKxHHWpMOpXm7LoNpdRAVZkEs+vPTWEB56yO+OeGwjhE/z5IrhmXN8pd8arFaAhLdQL+o8GZC1zkWIf78SzkURvW+356M8gtdSfwEoAhaAYjxPbLM5xR7qblulu/WtJRPAsOgvZ/ANiIfGfUiSt+5hHclQLDY0GjRokIl/AZe4aMYZ7Ye9AAAAAElFTkSuQmCC"},
		{Purpose: "Educational Payments", Icon: "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"},
	}

	for _, pt := range paymentTypes {
		if err := DB.FirstOrCreate(&pt, models.PaymentType{Purpose: pt.Purpose}).Error; err != nil {
			return fmt.Errorf("failed to seed PaymentType [%s]: %w", pt.Purpose, err)
		}
	}

	// Seed Questions

	questions := []models.Question{
		{
			Question: "Do you currently hold or have you ever held a significant public office?",
			Header:   "Declaration of status as a Politically Exposed Person (PEP)",
		},
		{
			Question: "Do you have tax obligations in another country?",
			Header:   "Tax compliance confirmation",
		},
	}

	for _, q := range questions {
		if err := DB.FirstOrCreate(&q, models.Question{Question: q.Question}).Error; err != nil {
			return fmt.Errorf("failed to seed Question [%s]: %w", q.Question, err)
		}
	}
	charges := []models.Charge{
		{
			Name:        "wallet_transfer",
			Description: "Service charge for wallet to wallet transfer",
			Amount:      5.00,
			Currency:    "USD",
			IsActive:    true,
		},
		{
			Name:        "card_topup",
			Description: "Service charge for card topup",
			Amount:      5.00,
			Currency:    "USD",
			IsActive:    true,
		},
	}

	for _, charge := range charges {
		if err := DB.Create(&charge).Error; err != nil {
			return err
		}
	}
	return nil

}
