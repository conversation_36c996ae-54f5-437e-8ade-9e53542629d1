package RequestDTO

type UserDetailsUpdateDTO struct {
	Email                  string `gorm:"size:100;not null;unique" json:"email"`                                               // Unique email for lookup
	DateOfBirth            string `gorm:"size:50" json:"date_of_birth"`                                                        // Stored as string (YYYY-MM-DD)
	Nationality            string `gorm:"size:100" json:"nationality"`
	PlaceOfBirth           string `gorm:"size:100" json:"place_of_birth"`
	NIF                    string `gorm:"size:50;unique" json:"nif"` // Unique Tax ID
	Street                 string `gorm:"size:255" json:"street"`
	Province               string `gorm:"size:20" json:"province"`
	PostalCode             string `gorm:"size:20" json:"postal_code"`
	City                   string `gorm:"size:100" json:"city"`
	Country                string `gorm:"size:100" json:"country"`
	Occupation             string `gorm:"size:100" json:"occupation"`
	Company                string `gorm:"size:255" json:"company"`
	EstimatedIncome        string `gorm:"size:50" json:"estimated_income"` // EAI (Estimated Annual Income)
	SourceOfFunds          string `gorm:"size:255" json:"source_of_funds"`
	NicFrontBase64         string `gorm:"size:255" json:"nic_front_base64"` // URL to Front NIC Image (National Identity Card)
	NicBackBase64          string `gorm:"size:255" json:"nic_back_base64"`  // URL to Back NIC Image (National Identity Card)
	UserPhotoBase64        string `gorm:"size:255" json:"user_photo_base64"`
	PageIndex              int    `gorm:"column:page_index" json:"page_index"`
	IsVerify               bool   `gorm:"default:false" json:"is_verify"`                 // Is the user verified?
	IsPep                  bool   `gorm:"default:false" json:"is_pep"`                    // Is the user a Politically Exposed Person?
	IsTaxResidentElsewhere bool   `gorm:"default:false" json:"is_tax_resident_elsewhere"` // Is the user a tax resident elsewhere?
}