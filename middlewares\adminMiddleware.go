package middlewares

import (
	"net/http"
	"strings"

	"github.com/dgrijalva/jwt-go"
	"github.com/labstack/echo/v4"
)

func AdminMiddleware(next echo.HandlerFunc) echo.HandlerFunc {
	return func(c echo.Context) error {
		// Get token from the Authorization header
		authHeader := c.Request().Header.Get("Authorization")
		if authHeader == "" {
			return c.JSON(http.StatusUnauthorized, echo.Map{"error": "Authorization header missing"})
		}

		// Extract token from "Bearer <token>"
		tokenString := authHeader[len("Bearer "):]

		// Parse and validate JWT token
		token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, echo.NewHTTPError(http.StatusUnauthorized, "unexpected signing method")
			}
			return jwtSecret, nil
		})

		if err != nil || !token.Valid {
			return c.JSON(http.StatusUnauthorized, echo.Map{"error": "invalid token"})
		}

		// Extract claims
		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			return c.JSON(http.StatusUnauthorized, echo.Map{"error": "invalid token claims"})
		}

		// Check if user has admin role
		role, ok := claims["role"].(string)
		if !ok || !strings.HasPrefix(role, "super_admin") {
			return c.JSON(http.StatusForbidden, echo.Map{"error": "insufficient permissions"})
		}

		// Store claims in context
		c.Set("email", claims["email"])
		c.Set("admin_id", claims["admin_id"])
		c.Set("role", role)

		return next(c)
	}
}
