package services

import (
	"auth-service/config"
	resDTO "auth-service/dtos/Response"
	"auth-service/models"
	"crypto/rand"
	"fmt"
	"log"
	"math/big"
	"net/http"
	"strconv"

	"github.com/labstack/echo/v4"
)

// Generate a random 12-digit wallet account ID
func GenerateWalletAccountID() string {
	max := new(big.Int).Exp(big.NewInt(10), big.NewInt(12), nil) // 10^12
	n, err := rand.Int(rand.Reader, max)
	if err != nil {
		log.Fatal(err)
	}
	return fmt.Sprintf("%012d", n)
}

// CreateWalletAccount assigns a new account ID to a user
func CreateWalletAccount(userID uint) (string, error) {
	accountID := GenerateWalletAccountID()

	initialAmount, err := EncryptAES("0.00")
	if err != nil {
		print("ERROR ENCRYPTING INITIAL AMOUNT")
	}

	wallet := models.Wallet{
		UserID: userID,
		ID:     accountID,
		Amount: initialAmount,
	}

	err = config.DB.Create(&wallet).Error
	if err != nil {
		return "", err
	}

	fmt.Println("Wallet Account Created:", accountID)
	return accountID, nil
}

func ViewUserBalance(c echo.Context) error {
	userID, err := InterfaceToUint(c.Get("user_id"))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error converting user ID"})
	}

	wallet, err := GetWalletAmountSender(config.DB, userID)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: "Error fetching wallet amount"})
	}
	
	walletBalanceStr, err := DecryptAES(wallet.Amount)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, fmt.Errorf("error decrypting user balance"))
	}

	balance, err := strconv.ParseFloat(walletBalanceStr, 64)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, fmt.Errorf("error parsing user balance"))
	}

	return c.JSON(http.StatusOK, resDTO.ViewBalanceResponseDTO{
		APIResponse: resDTO.APIResponse{
			IsSuccess: true,
			Message:   "successfully fetched user balance",
		},
		Balance: balance,
	})
}
