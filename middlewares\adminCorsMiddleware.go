package middlewares

import (
	"github.com/labstack/echo/v4"
	"github.com/labstack/echo/v4/middleware"
)

func AdminCORS() echo.MiddlewareFunc {
	return middleware.CORSWithConfig(middleware.CORSConfig{
		AllowOrigins: []string{"*"}, // In production, replace with your admin frontend domain
		AllowMethods: []string{echo.GET, echo.HEAD, echo.PUT, echo.PATCH, echo.POST, echo.DELETE, echo.OPTIONS},
		AllowHeaders: []string{"*"},
		// echo.HeaderOrigin,
		// echo.HeaderContentType,
		// echo.HeaderAccept,
		// echo.HeaderAuthorization,
		// "X-Requested-With",
		// "X-CSRF-Token",
		//},
		AllowCredentials: false,
		MaxAge:           300,
		ExposeHeaders: []string{
			"Content-Length",
			"Content-Type",
		},
	})
}
