package models

import (
	"time"

	"gorm.io/gorm"
)

type KYCVerification struct {
	ID                     uint       `gorm:"primaryKey"`
	UserID                 uint       `gorm:"not null"` // Foreign key to users table
	VerificationSessionID  string     `gorm:"not null;size:255"`
	Status                 string     `gorm:"nullable;size:255"`
	DocumentType           string     `gorm:"nullable;size:255"` // passport, id_card, etc.
	LastErrorCode          string     `gorm:"nullable;size:255"` // document_expired, etc.
	VerifiedAt             *time.Time `gorm:"nullable"`
	SelfieCheckPassed      bool       `gorm:"default:false"`
	KYCAttempts            int        `gorm:"default:0"`
	SelfieErrorReason      string     `gorm:"nullable;size:255"` // document_expired, etc.
	FlaggedForManualReview bool       `gorm:"default:false"`
	User                   User       `gorm:"foreignKey:UserID;references:id"`

	gorm.Model
}
