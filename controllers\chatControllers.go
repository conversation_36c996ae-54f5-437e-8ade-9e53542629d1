package controllers

import (
	"auth-service/services"

	"github.com/labstack/echo/v4"
)

func ChatController(c echo.Context) error {
	return services.HandleChat(c)
}

func ContactController(c echo.Context) error {
	return services.PayContacts(c)
}

// New controllers for background connections
func BackgroundController(c echo.Context) error {
	return services.UpdateBackgroundConnection(c)
}

func GetConversationList(c echo.Context) error {
	return services.GetUserConversations(c)
}

func GetFullConversation(c echo.Context) error {
	return services.GetFullConversation(c)
}