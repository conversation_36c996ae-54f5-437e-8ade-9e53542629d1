package services

import (
	"auth-service/constants"
	reqDTO "auth-service/dtos/Request"
	resDTO "auth-service/dtos/Response"
	"math/rand"

	"auth-service/config"
	"fmt"
	"net/http"
	"sync"
	"time"

	"github.com/labstack/echo/v4"
	"gopkg.in/gomail.v2"
)

var (
	otpStore = make(map[string]OTPData)
	otpMutex sync.Mutex
)

type OTPData struct {
	Code       string
	ExpiresAt  time.Time
	LastSentAt time.Time
}

// Verify OTP
func VerifyOTP(c echo.Context) error {
	c.Set("serviceName", "otp_service")
	c.Set("moduleName", "services")
	message := "OTP verified successfully."

	var req reqDTO.VerifyOTPRequest
	if err := c.Bind(&req); err != nil {
		message = "Invalid request"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message})
	}

	otpMutex.Lock()
	otpData, exists := otpStore[req.Email]
	otpMutex.Unlock()

	if !exists || otpData.Code != req.OTP {
		message = "Invalid OTP"
		c.Set("customMessage", message)
		return c.JSON(http.StatusUnauthorized, resDTO.APIResponse{Message: message})
	}

	if time.Now().After(otpData.ExpiresAt) {
		message = "OTP expired"
		c.Set("customMessage", message)
		return c.JSON(http.StatusUnauthorized, resDTO.APIResponse{Message: message})
	}

	// Activate user
	//config.DB.Model(&res.User{}).Where("email = ?", req.Email).Update("is_active", true)

	// Remove OTP after verification
	otpMutex.Lock()
	delete(otpStore, req.Email)
	otpMutex.Unlock()

	c.Set("customMessage", message)
	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
}

// Resend OTP with rate limiting
func ResendOTP(c echo.Context) error {
	message := "New OTP sent."

	c.Set("serviceName", "otp_service")
	c.Set("moduleName", "services")

	var req reqDTO.ResendOTPRequest

	// Bind request body
	if err := c.Bind(&req); err != nil {
		message = "Invalid request"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message})
	}

	// Check OTP rate limiting
	otpMutex.Lock()
	otpData, exists := otpStore[req.Email]
	if exists && time.Since(otpData.LastSentAt) < time.Minute {
		otpMutex.Unlock()
		message = "Please wait before requesting a new OTP."
		c.Set("customMessage", message)
		return c.JSON(http.StatusTooManyRequests, resDTO.APIResponse{Message: message})
	}
	otpMutex.Unlock()
	otp := generateOTP()

	// Uncomment to send OTP
	SendOTPF(req.Email, "Your otp code", otp)

	c.Set("customMessage", message)
	return c.JSON(http.StatusOK, resDTO.APIResponse{IsSuccess: true, Message: message})
}

// Generate a random 4-digit OTP
func generateOTP() string {
	//return fmt.Sprintf("%04d", rand.Intn(1000000))
	rand.Seed(time.Now().UnixNano())
	return fmt.Sprintf("%04d", rand.Intn(9000)+1000) // range: 1000–9999

	//return fmt.Sprintf("%04d", 1234)
}

// Background Goroutine for OTP Cleanup
func StartOTPCleanup() {
	go func() {
		for {
			time.Sleep(1 * time.Minute)
			otpMutex.Lock()
			for email, otpData := range otpStore {
				if time.Now().After(otpData.ExpiresAt) {
					delete(otpStore, email)
				}
			}
			otpMutex.Unlock()
		}
	}()
}

func SendOTP(c echo.Context) error {
	message := ""

	c.Set("serviceName", "otp_service")
	c.Set("moduleName", "services")

	var req reqDTO.ResendOTPRequest

	// Bind request body
	err := c.Bind(&req)
	if err != nil {
		message = "Invalid request"
		c.Set("customMessage", message)
		return c.JSON(http.StatusBadRequest, resDTO.APIResponse{Message: message})
	}

	// Fetch user by email
	user, err := getUserByEmail(config.DB, req.Email)
	if err != nil {
		message = "Error getting email from database"
		c.Set("customMessage", message)
		return c.JSON(http.StatusInternalServerError, resDTO.APIResponse{Message: message})
	}

	// Check if email exists
	if user.Email == "" {
		message = "Email not found"
		c.Set("customMessage", message)
		return c.JSON(http.StatusNotFound, resDTO.APIResponse{Message: message})
	}

	// Generate OTP and store it
	otp := generateOTP()
	expiration := time.Now().Add(5 * time.Minute)
	otpMutex.Lock()
	otpStore[req.Email] = OTPData{Code: otp, ExpiresAt: expiration, LastSentAt: time.Now()}
	otpMutex.Unlock()

	// Uncomment this code to send email
	// m := gomail.NewMessage()
	// m.SetHeader("From", "<EMAIL>")
	// m.SetHeader("To", req.Email)
	// m.SetHeader("Subject", "Your OTP Code")
	// m.SetBody("text/plain", "Your OTP is: "+otp)

	// d := gomail.NewDialer("smtp.gmail.com", 587, "<EMAIL>", "Mercedesbenz20")

	// err1 := d.DialAndSend(m)
	// if err1 != nil {
	// 	message = "Error sending OTP"
	// 	return c.JSON(http.StatusInternalServerError, res.APIResponse{Message: message})
	// } else {
	// 	message = "OTP Sent Successfully"
	// 	return c.JSON(http.StatusOK, res.APIResponse{Message: message})
	// }

	return nil
}

func SendOTPF(email, subject, otp string) error {
	// Generate OTP and store it
	expiration := time.Now().Add(2 * time.Minute)
	otpMutex.Lock()
	otpStore[email] = OTPData{Code: otp, ExpiresAt: expiration, LastSentAt: time.Now()}
	otpMutex.Unlock()
	body := constants.OTPBody(otp)
	err := SendOTPEmail(email, subject, body)
	if err != nil {
		return err
	}
	return nil
}

func SendOTPEmail(toEmail, subject, body string) error {

	//TODO: get values from static_configurations table
	m := gomail.NewMessage()

	m.SetHeader("From", "<EMAIL>")
	m.SetHeader("To", toEmail)
	m.SetHeader("Subject", subject)
	m.SetBody("text/plain", body)

	d := gomail.NewDialer("mail.devcustomprojects.com", 465, "<EMAIL>", "s{8QBiBv8_;h")

	if err := d.DialAndSend(m); err != nil {
		return err
	}
	return nil
}
