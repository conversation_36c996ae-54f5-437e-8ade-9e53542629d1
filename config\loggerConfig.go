package config

import (
	"fmt"
	"os"
	"time"

	"github.com/rs/zerolog"
	"gopkg.in/natefinch/lumberjack.v2"
)

func ConfigureBaseLogger() zerolog.Logger {
	logFilePath := os.Getenv("LOG_FILE")
	fmt.Printf("Log file path: %s\n", logFilePath)

	if logFilePath == "" {
		logFilePath = "./ws_api_default.log"
		fmt.Printf("Using default log file path: %s\n", logFilePath)
	}

	fileRotationConfig := &lumberjack.Logger{
		Filename:   logFilePath,
		MaxSize:    10, // MB
		MaxBackups: 0,
		MaxAge:     0,
		LocalTime:  false,
		Compress:   true, // Has to be true in order for the rotated log file's name to have timestamp
	}

	zerolog.TimeFieldFormat = time.RFC3339

	return zerolog.New(fileRotationConfig).
		Level(zerolog.InfoLevel).
		With().
		Timestamp().
		Caller().
		Logger()
}
